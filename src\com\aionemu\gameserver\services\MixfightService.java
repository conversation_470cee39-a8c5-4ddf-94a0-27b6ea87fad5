package com.aionemu.gameserver.services;
import com.aionemu.gameserver.utils.PositionUtil;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.stream.Collectors;

import com.aionemu.gameserver.configs.main.CustomConfig;
import com.aionemu.gameserver.model.ChatType;
import com.aionemu.gameserver.model.gameobjects.Npc;
import com.aionemu.gameserver.model.gameobjects.player.CustomPlayerState;
import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.model.gameobjects.LetterType;
import com.aionemu.gameserver.model.templates.spawns.SpawnTemplate;
import com.aionemu.gameserver.services.instance.InstanceService;
import com.aionemu.gameserver.services.mail.SystemMailService;
import com.aionemu.gameserver.services.player.PlayerReviveService;
import com.aionemu.gameserver.services.teleport.TeleportService;
import com.aionemu.gameserver.spawnengine.SpawnEngine;
import com.aionemu.gameserver.utils.PacketSendUtility;
import com.aionemu.gameserver.utils.ThreadPoolManager;
import com.aionemu.gameserver.world.World;
import com.aionemu.gameserver.world.WorldMapInstance;

import com.aionemu.gameserver.model.team.group.PlayerGroupService;
import com.aionemu.gameserver.model.team.alliance.PlayerAllianceService;

// Masking imports
import com.aionemu.gameserver.dataholders.DataManager;
import com.aionemu.gameserver.model.gameobjects.Item;
import com.aionemu.gameserver.model.templates.item.ItemTemplate;
import com.aionemu.gameserver.network.aion.serverpackets.SM_CUSTOM_SETTINGS;
import com.aionemu.gameserver.network.aion.serverpackets.SM_UPDATE_PLAYER_APPEARANCE;
import com.aionemu.gameserver.model.items.ItemSlot;
import com.aionemu.gameserver.model.EmotionType;
import com.aionemu.gameserver.network.aion.serverpackets.SM_EMOTION;
import com.aionemu.gameserver.network.aion.serverpackets.SM_SYSTEM_MESSAGE;
import com.aionemu.gameserver.model.Race;
import java.util.concurrent.TimeUnit;

/**
 * MixFight Event Service - Custom PvP Event System with Masking
 */
public class MixfightService {

    // Logging
    private static void logInfo(String message, Object... args) {
        System.out.println("[MixfightService] INFO: " + String.format(message, args));
    }

    private static void logWarn(String message, Object... args) {
        System.out.println("[MixfightService] WARN: " + String.format(message, args));
    }

    private static void logError(String message, Object... args) {
        System.out.println("[MixfightService] ERROR: " + String.format(message, args));
    }

    private static void logDebug(String message, Object... args) {
        System.out.println("[MixfightService] DEBUG: " + String.format(message, args));
    }

    // Event state
    private boolean eventActive = false;
    private boolean portalsActive = false;
    private WorldMapInstance currentEventInstance;
    private int currentEventMapId;

    // Portal NPCs
    private Npc sanctumPortalNpc;
    private Npc pandaemoniumPortalNpc;

    // Participant tracking
    private final Map<Integer, MixfightParticipant> participants = new ConcurrentHashMap<>();
    private final Map<Integer, Integer> eliminatedPlayers = new ConcurrentHashMap<>();
    private final Set<Integer> eventMaps = new HashSet<>();
    private final Map<Integer, String> playerRegistrationLocations = new ConcurrentHashMap<>();
    private final Map<Integer, Long> playerDeathTimes = new ConcurrentHashMap<>();
	
	// Add new scheduler for timed announcements
    private ScheduledFuture<?> timedAnnouncementTask;
    private final Map<Long, Boolean> announcedTimes = new ConcurrentHashMap<>();

    // Scheduled tasks
    private ScheduledFuture<?> eventTask;
    private ScheduledFuture<?> portalTask;
    private ScheduledFuture<?> deathMonitorTask;
    private final List<ScheduledFuture<?>> announcementTasks = new ArrayList<>();
    
    // Masking system
    private final ConcurrentHashMap<Integer, List<Item>> originalEquipment = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<Integer, String> originalNames = new ConcurrentHashMap<>();
    private static final String MIXFIGHT_DISPLAY_NAME = "MixFight-Challenger";
    private static final int MIXFIGHT_COSTUME_BODY_ID = 110900416;
    private static final int MIXFIGHT_COSTUME_HEAD_ID = 125045246;
    private ScheduledFuture<?> costumeRefreshTask;

    // Time tracking
    private long eventStartTime = 0;
    private long portalStartTime = 0;

    // Spawn coordinates cache
    private final Map<Integer, List<float[]>> spawnCoordinatesCache = new HashMap<>();

    // Singleton pattern
    private static class SingletonHolder {
        protected static final MixfightService instance = new MixfightService();
    }

    public static MixfightService getInstance() {
        return SingletonHolder.instance;
    }

    private MixfightService() {
        // Private constructor
    }

    public void init() {
        if (!CustomConfig.MIXFIGHT_ENABLED) {
            logInfo("MixFight Event system is disabled.");
            return;
        }

        logInfo("Initializing MixFight Event service...");
        parseEventMaps();
        scheduleEvents();
        logInfo("MixFight Event service initialized successfully!");
    }

    private void parseEventMaps() {
        String[] mapIds = CustomConfig.MIXFIGHT_MAPS.split(",");
        for (String mapIdStr : mapIds) {
            try {
                int mapId = Integer.parseInt(mapIdStr.trim());
                eventMaps.add(mapId);
                logDebug("Added MixFight event map: %s", mapId);
            } catch (NumberFormatException e) {
                logWarn("Invalid map ID in MixFight configuration: %s", mapIdStr);
            }
        }

        if (eventMaps.isEmpty()) {
            logWarn("No valid event maps configured for MixFight!");
        }
    }

    private void scheduleEvents() {
        try {
            logInfo("MixFight events scheduled with cron: %s", CustomConfig.MIXFIGHT_SCHEDULE);
        } catch (Exception e) {
            logError("Failed to schedule MixFight events: %s", e.getMessage());
        }
    }

    public void startEventSequence() {
        if (eventActive || portalsActive) {
            logWarn("MixFight event already active, skipping new event");
            return;
        }

        logInfo("Starting MixFight event sequence");
        scheduleAnnouncements();
        startPortals();
        
        portalTask = ThreadPoolManager.getInstance().schedule(() -> {
            startEvent();
        }, CustomConfig.MIXFIGHT_PORTAL_DURATION_MINUTES * 60 * 1000);
    }

    private void scheduleAnnouncements() {
        if (!CustomConfig.MIXFIGHT_ANNOUNCEMENTS) {
            return;
        }

        String[] intervals = CustomConfig.MIXFIGHT_ANNOUNCEMENT_INTERVALS.split(",");
        for (String intervalStr : intervals) {
            try {
                int minutes = Integer.parseInt(intervalStr.trim());
                long delay = (CustomConfig.MIXFIGHT_PORTAL_DURATION_MINUTES - minutes) * 60 * 1000;

                if (delay > 0) {
                    ScheduledFuture<?> task = ThreadPoolManager.getInstance().schedule(() -> {
                        announceEvent(minutes);
                    }, delay);
                    announcementTasks.add(task);
                }
            } catch (NumberFormatException e) {
                logWarn("Invalid announcement interval: %s", intervalStr);
            }
        }
    }

    private void announceEvent(int minutesRemaining) {
        String message = String.format(
            "MixFight Event starts in %d minute%s! Portals are now open in Sanctum and Pandaemonium. Level %d+ players can participate!",
            minutesRemaining, minutesRemaining == 1 ? "" : "s", CustomConfig.MIXFIGHT_MIN_LEVEL
        );

        World.getInstance().forEachPlayer(player -> {
            PacketSendUtility.sendMessage(player, message, ChatType.BRIGHT_YELLOW_CENTER);
        });

        logInfo("Announced MixFight event: %s minutes remaining", minutesRemaining);
    }

    private void startPortals() {
        portalsActive = true;
        portalStartTime = System.currentTimeMillis();
        clearExistingPortals();
        spawnSanctumPortal();
        spawnPandaemoniumPortal();

        if (CustomConfig.MIXFIGHT_ANNOUNCEMENTS) {
            String message = String.format(
                "MixFight Event portals are now open! Event starts in %d minutes. Level %d+ players can enter!",
                CustomConfig.MIXFIGHT_PORTAL_DURATION_MINUTES, CustomConfig.MIXFIGHT_MIN_LEVEL
            );

            World.getInstance().forEachPlayer(player -> {
                PacketSendUtility.sendMessage(player, message, ChatType.BRIGHT_YELLOW_CENTER);
            });
        }

        logInfo("MixFight portals activated");
    }

    private void clearExistingPortals() {
        try {
            if (sanctumPortalNpc != null) {
                sanctumPortalNpc.getController().delete();
                sanctumPortalNpc = null;
            }

            if (pandaemoniumPortalNpc != null) {
                pandaemoniumPortalNpc.getController().delete();
                pandaemoniumPortalNpc = null;
            }

            logDebug("Cleared existing MixFight portals");
        } catch (Exception e) {
            logError("Error clearing existing portals: %s", e.getMessage());
        }
    }

    private void spawnSanctumPortal() {
        try {
            if (sanctumPortalNpc != null && sanctumPortalNpc.isSpawned()) {
                sanctumPortalNpc.getController().delete();
                sanctumPortalNpc = null;
            }

            String[] coords = CustomConfig.MIXFIGHT_PORTAL_SANCTUM_LOCATION.split(",");
            float x = Float.parseFloat(coords[0]);
            float y = Float.parseFloat(coords[1]);
            float z = Float.parseFloat(coords[2]);
            byte h = Byte.parseByte(coords[3]);

            SpawnTemplate spawn = SpawnEngine.newSingleTimeSpawn(110010000, CustomConfig.MIXFIGHT_PORTAL_SANCTUM_NPC,
                x, y, z, h, 0, "mixfight_portal");
            sanctumPortalNpc = (Npc) SpawnEngine.spawnObject(spawn, 0);

            if (sanctumPortalNpc != null) {
                logDebug("Successfully spawned Sanctum MixFight portal");
            }
        } catch (Exception e) {
            logError("Failed to spawn Sanctum MixFight portal: %s", e.getMessage());
        }
    }

    private void spawnPandaemoniumPortal() {
        try {
            if (pandaemoniumPortalNpc != null && pandaemoniumPortalNpc.isSpawned()) {
                pandaemoniumPortalNpc.getController().delete();
                pandaemoniumPortalNpc = null;
            }

            String[] coords = CustomConfig.MIXFIGHT_PORTAL_PANDAEMONIUM_LOCATION.split(",");
            float x = Float.parseFloat(coords[0]);
            float y = Float.parseFloat(coords[1]);
            float z = Float.parseFloat(coords[2]);
            byte h = Byte.parseByte(coords[3]);

            SpawnTemplate spawn = SpawnEngine.newSingleTimeSpawn(120010000, CustomConfig.MIXFIGHT_PORTAL_PANDAEMONIUM_NPC,
                x, y, z, h, 0, "mixfight_portal");
            pandaemoniumPortalNpc = (Npc) SpawnEngine.spawnObject(spawn, 0);

            if (pandaemoniumPortalNpc != null) {
                logDebug("Successfully spawned Pandaemonium MixFight portal");
            }
        } catch (Exception e) {
            logError("Failed to spawn Pandaemonium MixFight portal: %s", e.getMessage());
        }
    }

    private void startEvent() {
        if (eventActive) {
            logWarn("MixFight event already active");
            return;
        }

        currentEventMapId = selectRandomEventMap();
        if (currentEventMapId == 0) {
            logError("No valid event map available, canceling event");
            cleanup();
            return;
        }

        currentEventInstance = InstanceService.getNextAvailableInstance(currentEventMapId, 0, (byte) 0,
            CustomConfig.MIXFIGHT_MAX_PLAYERS, true);

        clearInstanceMonsters();
        eventActive = true;
        eventStartTime = System.currentTimeMillis();
        closePortals();
        announceEventStart();
        teleportAllParticipants();
		
		// Start timed announcements
        startTimedAnnouncements();

        // Schedule event end
        eventTask = ThreadPoolManager.getInstance().schedule(() -> {
            endEvent();
        }, CustomConfig.MIXFIGHT_DURATION_MINUTES * 60 * 1000);

        // Start death monitoring
        deathMonitorTask = ThreadPoolManager.getInstance().scheduleAtFixedRate(() -> {
            monitorDeathStates();
        }, 1000, 500);
        
        // Start costume refresh
        costumeRefreshTask = ThreadPoolManager.getInstance().scheduleAtFixedRate(() -> {
            refreshCostumes();
        }, 8000, 8000);

        logInfo("MixFight event started on map %s with instance %s", currentEventMapId,
            currentEventInstance.getInstanceId());
    }
	/**
     * Start timed announcements for event duration
     */
    private void startTimedAnnouncements() {
    final long totalDurationMs = CustomConfig.MIXFIGHT_DURATION_MINUTES * 60 * 1000;
    final long startTime = System.currentTimeMillis();
    
    timedAnnouncementTask = ThreadPoolManager.getInstance().scheduleAtFixedRate(() -> {
        if (!eventActive) return;
        
        long elapsedMs = System.currentTimeMillis() - startTime;
        long remainingMs = totalDurationMs - elapsedMs;
        
        // Event has ended
        if (remainingMs <= 0) {
            timedAnnouncementTask.cancel(false);
            return;
        }
        
        // Calculate time units in milliseconds
        long remainingSec = remainingMs / 1000;
        long remainingMin = remainingSec / 60;
        
        // Phase 3: Last 10 seconds - announce every second
        if (remainingSec <= 10) {
            if (!announcedTimes.containsKey(remainingSec)) {
                announceToParticipants(remainingSec + " seconds remaining!");
                announcedTimes.put(remainingSec, true);
            }
            return;
        }
        
        // Phase 2: Last 5 minutes - announce every minute
        if (remainingSec <= 300) { // 5 minutes = 300 seconds
            if (remainingSec % 60 == 0 && !announcedTimes.containsKey(remainingMin)) {
                announceToParticipants(remainingMin + " minutes remaining!");
                announcedTimes.put(remainingMin, true);
            }
            return;
        }
        
        // Phase 1: Regular phase - announce every 10 minutes
        if (remainingMin % 10 == 0 && remainingSec % 60 == 0) {
            if (!announcedTimes.containsKey(remainingMin)) {
                announceToParticipants(remainingMin + " minutes remaining!");
                announcedTimes.put(remainingMin, true);
            }
        }
    }, 0, 1000); // Check every second (1000ms)
}

    /**
     * Announce message to all participants
     */
    private void announceToParticipants(String message) {
        for (MixfightParticipant participant : participants.values()) {
            Player player = World.getInstance().getPlayer(participant.getPlayerId());
            if (player != null) {
                PacketSendUtility.sendMessage(player, message, ChatType.BRIGHT_YELLOW_CENTER);
            }
        }
        logInfo("MixFight announcement: " + message);
    }

    private void refreshCostumes() {
        if (!eventActive) return;
        
        for (MixfightParticipant participant : participants.values()) {
            Player player = World.getInstance().getPlayer(participant.getPlayerId());
            if (player != null && player.isOnline()) {
                applyCostumeSilent(player);
            }
        }
    }

    private int selectRandomEventMap() {
        if (eventMaps.isEmpty()) {
            return 0;
        }

        List<Integer> mapList = new ArrayList<>(eventMaps);
        return mapList.get(new Random().nextInt(mapList.size()));
    }

    private void closePortals() {
        portalsActive = false;

        if (sanctumPortalNpc != null) {
            sanctumPortalNpc.getController().delete();
            sanctumPortalNpc = null;
        }

        if (pandaemoniumPortalNpc != null) {
            pandaemoniumPortalNpc.getController().delete();
            pandaemoniumPortalNpc = null;
        }

        logDebug("MixFight portals closed");
    }

    private void announceEventStart() {
        String message = String.format(
            "MixFight Event has begun! Duration: %d minutes. Fight for glory and rewards!",
            CustomConfig.MIXFIGHT_DURATION_MINUTES
        );

        for (MixfightParticipant participant : participants.values()) {
            Player player = World.getInstance().getPlayer(participant.getPlayerId());
            if (player != null) {
                PacketSendUtility.sendMessage(player, message, ChatType.BRIGHT_YELLOW_CENTER);
            }
        }

        logInfo("Announced MixFight event start to %s participants", participants.size());
    }

    private void endEvent() {
        if (!eventActive) {
            return;
        }

        logInfo("Ending MixFight event");
        distributeRewards();
        removeAllParticipants();
        cleanup();
        announceEventEnd();
    }

    private void distributeRewards() {
        if (participants.isEmpty()) {
            logInfo("No participants to reward");
            return;
        }

        logInfo("Distributing rank-based rewards to %s participants", participants.size());
        List<MixfightParticipant> finalLeaderboard = getLeaderboard();

        for (int i = 0; i < finalLeaderboard.size(); i++) {
            MixfightParticipant participant = finalLeaderboard.get(i);
            int rank = i + 1;
            RewardTier tier = determineRewardTierByRank(rank, finalLeaderboard.size());
            sendRewards(participant.getPlayerName(), tier, participant, rank);
        }
    }

    private RewardTier determineRewardTierByRank(int rank, int totalParticipants) {
        double percentile = (double) rank / totalParticipants;

        if (rank == 1) {
            return RewardTier.TIER1;
        } else if (percentile <= 0.10) {
            return RewardTier.TIER1;
        } else if (percentile <= 0.30) {
            return RewardTier.TIER2;
        } else if (percentile <= 0.60) {
            return RewardTier.TIER3;
        } else if (percentile <= 1.00) {
            return RewardTier.TIER4;
        } else {
            return RewardTier.PARTICIPATION;
        }
    }

    private int[] parseRewardConfig(String config) {
        String[] parts = config.split(":");
        int[] values = new int[parts.length];
        for (int i = 0; i < parts.length; i++) {
            values[i] = Integer.parseInt(parts[i]);
        }
        return values;
    }

    private void sendRewards(String playerName, RewardTier tier, MixfightParticipant participant, int rank) {
        int[] rewardData;
        String title = "MixFight Event Rewards";
        String message;

        if (tier == RewardTier.PARTICIPATION) {
            rewardData = parseRewardConfig(CustomConfig.MIXFIGHT_REWARDS_PARTICIPATION);
            message = String.format("Thank you for participating in the MixFight Event! You finished rank #%d with %d AP and %d kills.",
                rank, participant.getAccumulatedAP(), participant.getKills());
            SystemMailService.sendMail("MixFight System", playerName, title, message,
                rewardData[0], rewardData[1], rewardData[2], LetterType.EXPRESS);
        } else {
            String tierConfig = switch (tier) {
                case TIER1 -> CustomConfig.MIXFIGHT_REWARDS_TIER1;
                case TIER2 -> CustomConfig.MIXFIGHT_REWARDS_TIER2;
                case TIER3 -> CustomConfig.MIXFIGHT_REWARDS_TIER3;
                case TIER4 -> CustomConfig.MIXFIGHT_REWARDS_TIER4;
                default -> CustomConfig.MIXFIGHT_REWARDS_PARTICIPATION;
            };

            rewardData = parseRewardConfig(tierConfig);
            String rankMessage = switch (rank) {
                case 1 -> "CHAMPION! You won the MixFight Event!";
                case 2 -> "Excellent! You finished 2nd place!";
                case 3 -> "Great job! You finished 3rd place!";
                default -> String.format("Well done! You finished rank #%d!", rank);
            };

            message = String.format("%s You earned %d AP and achieved %d kills during the event. Tier: %s",
                rankMessage, participant.getAccumulatedAP(), participant.getKills(), tier.name());
            SystemMailService.sendMail("MixFight System", playerName, title, message,
                rewardData[1], rewardData[2], rewardData[3], LetterType.EXPRESS);
        }
    }

    private void removeAllParticipants() {
        for (MixfightParticipant participant : participants.values()) {
            Player player = World.getInstance().getPlayer(participant.getPlayerId());
            if (player != null) {
                removePlayerFromEvent(player, true);
            }
        }
        participants.clear();
        eliminatedPlayers.clear();
    }

    private void cleanup() {
        eventActive = false;
        portalsActive = false;
        eventStartTime = 0;
        portalStartTime = 0;
        currentEventInstance = null;
        currentEventMapId = 0;

        if (eventTask != null) {
            eventTask.cancel(false);
            eventTask = null;
        }

        if (portalTask != null) {
            portalTask.cancel(false);
            portalTask = null;
        }

        if (deathMonitorTask != null) {
            deathMonitorTask.cancel(false);
            deathMonitorTask = null;
        }
        
        if (costumeRefreshTask != null) {
            costumeRefreshTask.cancel(false);
            costumeRefreshTask = null;
        }

        for (ScheduledFuture<?> task : announcementTasks) {
            if (task != null) {
                task.cancel(false);
            }
        }
        announcementTasks.clear();
		
		// Cancel timed announcements
        if (timedAnnouncementTask != null) {
            timedAnnouncementTask.cancel(false);
            timedAnnouncementTask = null;
        }
        announcedTimes.clear();

        closePortals();
        playerRegistrationLocations.clear();
        playerDeathTimes.clear();
        cleanupAllMasking();


        logInfo("MixFight event cleanup completed");
    }
    
    private void cleanupAllMasking() {
        for (Integer playerId : new ArrayList<>(originalNames.keySet())) {
            Player player = World.getInstance().getPlayer(playerId);
            if (player != null && player.isOnline()) {
                removeMixfightMasking(player);
            } else {
                originalNames.remove(playerId);
                originalEquipment.remove(playerId);
            }
        }
    }

    private void announceEventEnd() {
        String message = "MixFight Event has ended! Check your mail for rewards.";
        World.getInstance().forEachPlayer(player -> {
            PacketSendUtility.sendMessage(player, message, ChatType.BRIGHT_YELLOW_CENTER);
        });
        logInfo("Announced MixFight event end");
    }

    public List<MixfightParticipant> getLeaderboard() {
        return participants.values().stream()
            .sorted((p1, p2) -> Integer.compare(p2.getAccumulatedAP(), p1.getAccumulatedAP()))
            .collect(Collectors.toList());
    }

    public boolean joinEvent(Player player) {
        if (!portalsActive && !eventActive) {
            PacketSendUtility.sendMessage(player, "MixFight event is not currently active.");
            return false;
        }

        if (player.getLevel() < CustomConfig.MIXFIGHT_MIN_LEVEL) {
            PacketSendUtility.sendMessage(player,
                "You must be at least level " + CustomConfig.MIXFIGHT_MIN_LEVEL + " to participate in MixFight.");
            return false;
        }

        if (participants.containsKey(player.getObjectId())) {
            PacketSendUtility.sendMessage(player, "You are already participating in the MixFight event.");
            return false;
        }

        if (eventActive && participants.size() >= CustomConfig.MIXFIGHT_MAX_PLAYERS) {
            PacketSendUtility.sendMessage(player, "MixFight event is full.");
            return false;
        }

        if (player.isInCustomState(CustomPlayerState.ONEVSONE_PARTICIPANT) ||
            player.isInCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS)) {
            PacketSendUtility.sendMessage(player, "You cannot join MixFight while participating in another event.");
            return false;
        }
		
        MixfightParticipant participant = new MixfightParticipant(player.getObjectId(), player.getName());
        participants.put(player.getObjectId(), participant);
        playerRegistrationLocations.put(player.getObjectId(), determineRegistrationLocation(player));
        player.setCustomState(CustomPlayerState.MIXFIGHT_PARTICIPANT);

        if (eventActive && currentEventInstance != null) {
            teleportToEventMap(player);
        } else {
            PacketSendUtility.sendMessage(player,
                "You have joined the MixFight event! You will be teleported when the event starts.");
        }

        logInfo("Player %s joined MixFight event", player.getName());
        return true;
    }
	private void manualResurrect(Player player) {
    try {
        // Use standard resurrection service to prevent resurrection popup
        PlayerReviveService.revive(player, 100, 100, false, 0);
        
        // Additional state cleanup
        player.unsetResPosState();
        player.setPlayerResActivate(false);
        player.getEffectController().removeEffect(8291); // Soul Sickness
        player.getGameStats().updateStatsAndSpeedVisually();
        
        // Force client update
        PacketSendUtility.broadcastPacket(player, new SM_EMOTION(player, EmotionType.RESURRECT, 0, 0), true);
    } catch (Exception e) {
        logError("Error during manual resurrection: %s", e.getMessage());
    }
}

// Update autoEliminateStuckPlayer
private void autoEliminateStuckPlayer(Player player) {
    try {
        manualResurrect(player); // Use manual resurrection
        eliminatePlayer(player);
    } catch (Exception e) {
        logError("Error auto-eliminating player: %s", e.getMessage());
    }
}


    private String determineRegistrationLocation(Player player) {
        int mapId = player.getWorldId();
        if (mapId == 110010000) {
            return "SANCTUM";
        } else if (mapId == 120010000) {
            return "PANDAEMONIUM";
        }

        if (player.getRace() == Race.ELYOS) {
            return "SANCTUM";
        } else {
            return "PANDAEMONIUM";
        }
    }

    public boolean leaveEvent(Player player) {
        if (!participants.containsKey(player.getObjectId())) {
            PacketSendUtility.sendMessage(player, "You are not participating in the MixFight event.");
            return false;
        }

        removePlayerFromEvent(player, true);
        PacketSendUtility.sendMessage(player, "You have left the MixFight event.");
        logInfo("Player %s left MixFight event", player.getName());
        return true;
    }

    public void handlePlayerDisconnect(Player player) {
        if (!CustomConfig.MIXFIGHT_ENABLED) {
            return;
        }

        if (participants.containsKey(player.getObjectId())) {
            logInfo("MixFight participant %s disconnected, cleaning up states", player.getName());
            removePlayerFromEvent(player, false);

            if (eventActive) {
                int placement = eliminatedPlayers.size() + participants.size() + 1;
                eliminatedPlayers.put(player.getObjectId(), placement);
                logInfo("Player %s eliminated due to disconnect. Placement: %d", player.getName(), placement);
                checkForEventEnd();
            }
        } else {
            if (player.isInCustomState(CustomPlayerState.MIXFIGHT_PARTICIPANT) ||
                player.isInCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS)) {

                logDebug("Cleaning up lingering MixFight states for disconnected player %s", player.getName());
                player.unsetCustomState(CustomPlayerState.MIXFIGHT_PARTICIPANT);
                player.unsetCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);
                player.getController().onChangedPlayerAttributes();
            }
        }
        
        if (originalNames.containsKey(player.getObjectId())) {
            removeMixfightMasking(player);
        }
    }

  // Updated removePlayerFromEvent method
private void removePlayerFromEvent(Player player, boolean teleportOut) {
    // 1. Clear states IMMEDIATELY
    player.unsetCustomState(CustomPlayerState.MIXFIGHT_PARTICIPANT);
    player.unsetCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);
    
    // 2. Force attribute update BEFORE teleport
    player.getController().onChangedPlayerAttributes();
    
    // 3. Remove masking BEFORE teleport
    removeMixfightMasking(player);
    
    // 4. RESURRECTION ALREADY HANDLED IN onPlayerDeath - REMOVED REDUNDANT CALL
    
    // 5. Refresh event participants' view
    refreshEventParticipantsView(player);
    
    // 6. Force full knownlist reset for all participants
    refreshAllParticipantsKnownlist();

    // 7. Teleport out immediately (no delay)
    if (teleportOut) {
        // Add small delay before teleportation
        ThreadPoolManager.getInstance().schedule(() -> {
            teleportToRegistrationLocation(player);
        }, 100);
    }
}


     // Fixed teleport method
    private void teleportToRegistrationLocation(Player player) {
        String registrationLocation = playerRegistrationLocations.get(player.getObjectId());
        if (registrationLocation == null) {
            TeleportService.moveToBindLocation(player);
            return;
        }

        try {
            String[] coords;
            if ("SANCTUM".equals(registrationLocation)) {
                coords = CustomConfig.MIXFIGHT_PORTAL_SANCTUM_LOCATION.split(",");
                TeleportService.teleportTo(player, 110010000, 
                    Float.parseFloat(coords[0]), 
                    Float.parseFloat(coords[1]), 
                    Float.parseFloat(coords[2]), 
                    Byte.parseByte(coords[3]));
            } else if ("PANDAEMONIUM".equals(registrationLocation)) {
                coords = CustomConfig.MIXFIGHT_PORTAL_PANDAEMONIUM_LOCATION.split(",");
                TeleportService.teleportTo(player, 120010000, 
                    Float.parseFloat(coords[0]), 
                    Float.parseFloat(coords[1]), 
                    Float.parseFloat(coords[2]), 
                    Byte.parseByte(coords[3]));
            } else {
                TeleportService.moveToBindLocation(player);
            }
        } catch (Exception e) {
            TeleportService.moveToBindLocation(player);
        }
    }

    private void updateAllPlayersEnemyState() {
        try {
            for (MixfightParticipant participant : participants.values()) {
                Player player = World.getInstance().getPlayer(participant.getPlayerId());
                if (player != null && player.isOnline()) {
                    player.setCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);
                    player.getController().onChangedPlayerAttributes();
                }
            }
        } catch (Exception e) {
            logError("Error updating enemy states: %s", e.getMessage());
        }
    }

    private void monitorDeathStates() {
        if (!eventActive || participants.isEmpty()) {
            return;
        }

        long currentTime = System.currentTimeMillis();
        final long DEATH_TIMEOUT_MS = 1000;

        try {
            for (MixfightParticipant participant : participants.values()) {
                Player player = World.getInstance().getPlayer(participant.getPlayerId());
                if (player == null || !player.isOnline()) {
                    playerDeathTimes.remove(participant.getPlayerId());
                    continue;
                }

                if (player.isDead()) {
                    Long deathTime = playerDeathTimes.get(participant.getPlayerId());
                    if (deathTime == null) {
                        playerDeathTimes.put(participant.getPlayerId(), currentTime);
                    } else if (currentTime - deathTime >= DEATH_TIMEOUT_MS) {
                        autoEliminateStuckPlayer(player);
                        playerDeathTimes.remove(participant.getPlayerId());
                    }
                } else {
                    playerDeathTimes.remove(participant.getPlayerId());
                }
            }
        } catch (Exception e) {
            logError("Error monitoring death states: %s", e.getMessage());
        }
    }



    private void clearInstanceMonsters() {
        if (currentEventInstance == null) {
            return;
        }

        int clearedCount = 0;
        for (Npc npc : currentEventInstance.getNpcs()) {
            if (npc != null && !npc.isDead()) {
                npc.getController().delete();
                clearedCount++;
            }
        }
        logInfo("Cleared %s monsters from MixFight instance", clearedCount);
    }

    private void teleportAllParticipants() {
        if (currentEventInstance == null) {
            return;
        }

        for (MixfightParticipant participant : participants.values()) {
            Player player = World.getInstance().getPlayer(participant.getPlayerId());
            if (player != null && player.isOnline()) {
                teleportToEventMap(player);
            }
        }
    }

    private void teleportToEventMap(Player player) {
        if (currentEventInstance == null) {
            return;
        }

        List<float[]> spawnCoords = getSpawnCoordinates(currentEventMapId);
        if (spawnCoords.isEmpty()) {
            return;
        }

        float[] coords = spawnCoords.get(new Random().nextInt(spawnCoords.size()));
        TeleportService.teleportTo(player, currentEventMapId, currentEventInstance.getInstanceId(),
            coords[0], coords[1], coords[2], (byte) coords[3]);

        ThreadPoolManager.getInstance().schedule(() -> {
            if (player.isOnline() && participants.containsKey(player.getObjectId())) {
                player.setCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);
                player.getController().onChangedPlayerAttributes();
                updateAllPlayersEnemyState();
                applyMixfightMasking(player);
            }
        }, 2000);
		// Remove player from any groups or alliances
    if (player.isInGroup()) {  // Changed to isInGroup()
        PlayerGroupService.removePlayer(player);
        PacketSendUtility.sendMessage(player, "You have been removed from your group for the Mixfight event", 
            ChatType.BRIGHT_YELLOW_CENTER);
    }
    if (player.isInAlliance()) {  // Changed to isInAlliance()
        PlayerAllianceService.removePlayer(player);
        PacketSendUtility.sendMessage(player, "You have been removed from your alliance for the Mixfight event", 
            ChatType.BRIGHT_YELLOW_CENTER);
    }
    }

    // MASKING SYSTEM ===========================================================
    private void applyMixfightMasking(Player player) {
        try {
            // Store and mask name
            originalNames.put(player.getObjectId(), player.getName());
            player.getCommonData().setName(MIXFIGHT_DISPLAY_NAME);

            // Store original appearance
            originalEquipment.put(player.getObjectId(), new ArrayList<>(player.getEquipment().getEquippedForAppearance()));
            
            // Apply costume
            applyCostumeSilent(player);
        } catch (Exception e) {
            logError("Masking error: %s", e.getMessage());
        }
    }

    private void applyCostumeSilent(Player player) {
        try {
            ItemTemplate bodyTemplate = DataManager.ITEM_DATA.getItemTemplate(MIXFIGHT_COSTUME_BODY_ID);
            ItemTemplate headTemplate = DataManager.ITEM_DATA.getItemTemplate(MIXFIGHT_COSTUME_HEAD_ID);
            
            if (bodyTemplate == null || headTemplate == null) {
                logError("Missing costume templates!");
                return;
            }
            
            List<Item> previewItems = new ArrayList<>();
            long previewItemsSlotMask = 0;

            // Body costume
            long bodySlotMask = bodyTemplate.getItemSlot();
            if (ItemSlot.isVisible(bodySlotMask)) {
                Item bodyItem = new Item(0, bodyTemplate, 1, true, bodySlotMask);
                previewItems.add(bodyItem);
                previewItemsSlotMask |= bodySlotMask;
            }

            // Head costume
            long headSlotMask = headTemplate.getItemSlot();
            if (ItemSlot.isVisible(headSlotMask)) {
                Item headItem = new Item(0, headTemplate, 1, true, headSlotMask);
                previewItems.add(headItem);
                previewItemsSlotMask |= headSlotMask;
            }

            // Add existing equipment (excluding slots covered by costume)
            for (Item visibleEquipment : player.getEquipment().getEquippedForAppearance()) {
                if ((visibleEquipment.getEquipmentSlot() & previewItemsSlotMask) == 0) {
                    previewItems.add(visibleEquipment);
                }
            }

            // Sort by equipment slot
            previewItems.sort(java.util.Comparator.comparingLong(Item::getEquipmentSlot));

            // Apply appearance
            int display = player.getPlayerSettings().getDisplay() | SM_CUSTOM_SETTINGS.HIDE_LEGION_CLOAK;
            if (previewItems.stream().anyMatch(item -> item.getEquipmentSlot() == ItemSlot.HELMET.getSlotIdMask())) {
                display &= ~SM_CUSTOM_SETTINGS.HIDE_HELMET;
            }
            if (previewItems.stream().anyMatch(item -> item.getEquipmentSlot() == ItemSlot.PLUME.getSlotIdMask())) {
                display &= ~SM_CUSTOM_SETTINGS.HIDE_PLUME;
            }

            // Send appearance packets
            PacketSendUtility.sendPacket(player, new SM_CUSTOM_SETTINGS(player.getObjectId(), 1, display, player.getPlayerSettings().getDeny()));
            PacketSendUtility.sendPacket(player, new SM_UPDATE_PLAYER_APPEARANCE(player.getObjectId(), previewItems));
            PacketSendUtility.broadcastPacket(player, new SM_UPDATE_PLAYER_APPEARANCE(player.getObjectId(), previewItems), false);
            
        } catch (Exception e) {
            logError("Costume error: %s", e.getMessage());
        }
    }

    private void removeMixfightMasking(Player player) {
        try {
            // Restore name
            String originalName = originalNames.remove(player.getObjectId());
            if (originalName != null && MIXFIGHT_DISPLAY_NAME.equals(player.getName())) {
                player.getCommonData().setName(originalName);
            }

            // Restore appearance
            List<Item> originalItems = originalEquipment.remove(player.getObjectId());
            if (originalItems != null) {
                restoreOriginalAppearance(player, originalItems);
            }
        } catch (Exception e) {
            logError("Mask removal error: %s", e.getMessage());
        }
    }
    
    private void restoreOriginalAppearance(Player player, List<Item> originalItems) {
        PacketSendUtility.sendPacket(player, new SM_CUSTOM_SETTINGS(player));
        PacketSendUtility.sendPacket(player, new SM_UPDATE_PLAYER_APPEARANCE(player.getObjectId(), originalItems));
        PacketSendUtility.broadcastPacket(player, new SM_UPDATE_PLAYER_APPEARANCE(player.getObjectId(), originalItems), false);
    }
    
    private String getMixfightDisplayName(Player player) {
        return originalNames.containsKey(player.getObjectId()) ? 
               MIXFIGHT_DISPLAY_NAME : player.getName();
    }
    // END MASKING SYSTEM =======================================================

    private List<float[]> getSpawnCoordinates(int mapId) {
        if (spawnCoordinatesCache.containsKey(mapId)) {
            return spawnCoordinatesCache.get(mapId);
        }

        List<float[]> coords = new ArrayList<>();
        String coordsConfig = null;

        switch (mapId) {
            case 300030000: coordsConfig = CustomConfig.MIXFIGHT_SPAWN_300030000; break;
            case 300040000: coordsConfig = CustomConfig.MIXFIGHT_SPAWN_300040000; break;
            case 300250000: coordsConfig = CustomConfig.MIXFIGHT_SPAWN_300250000; break;
        }

        if (coordsConfig != null) {
            String[] spawnPoints = coordsConfig.split(";");
            for (String spawnPoint : spawnPoints) {
                try {
                    String[] values = spawnPoint.split(",");
                    if (values.length >= 4) {
                        float[] coord = new float[4];
                        coord[0] = Float.parseFloat(values[0]);
                        coord[1] = Float.parseFloat(values[1]);
                        coord[2] = Float.parseFloat(values[2]);
                        coord[3] = Float.parseFloat(values[3]);
                        coords.add(coord);
                    }
                } catch (NumberFormatException e) {
                    logWarn("Invalid spawn coordinate: %s", spawnPoint);
                }
            }
        }

        spawnCoordinatesCache.put(mapId, coords);
        return coords;
    }

    public void onPlayerKill(Player killer, Player victim) {
        if (!eventActive) {
            return;
        }

        MixfightParticipant killerParticipant = participants.get(killer.getObjectId());
        MixfightParticipant victimParticipant = participants.get(victim.getObjectId());
        if (killerParticipant == null || victimParticipant == null) {
            return;
        }

        int baseAP = 100;
        int bonusAP = (int) (baseAP * CustomConfig.MIXFIGHT_POINTS_MULTIPLIER);
        killerParticipant.addAP(bonusAP);
        killerParticipant.incrementKills();

        String killerName = getMixfightDisplayName(killer);
        String victimName = getMixfightDisplayName(victim);
        String message = String.format("%s eliminated %s! +%d AP", killerName, victimName, bonusAP);
        
        for (MixfightParticipant participant : participants.values()) {
            Player p = World.getInstance().getPlayer(participant.getPlayerId());
            if (p != null && p.isOnline()) {
                PacketSendUtility.sendMessage(p, message);
            }
        }

        logInfo("MixFight kill: %s killed %s (+%d AP)", killerName, victimName, bonusAP);
    }

    public void onPlayerDeath(Player player) {
    if (!eventActive) {
        return;
    }

    MixfightParticipant participant = participants.get(player.getObjectId());
    if (participant == null) {
        return;
    }

    // IMMEDIATE RESURRECTION TO PREVENT POPUP
    manualResurrect(player);
    playerDeathTimes.remove(player.getObjectId()); // Remove from death tracking

    // Add delay before teleportation to ensure resurrection completes
    ThreadPoolManager.getInstance().schedule(() -> {
        // Re-validate player state after delay
        Player refreshedPlayer = World.getInstance().getPlayer(player.getObjectId());
        if (refreshedPlayer == null || refreshedPlayer.getLifeStats().isDead()) { // FIXED: Changed isAlreadyDead() to isDead()
            logDebug("Player %s not found or still dead after resurrection", player.getName());
            return;
        }

        participant.incrementDeaths();
        int placement = participants.size() + eliminatedPlayers.size();
        eliminatedPlayers.put(player.getObjectId(), placement);
        
        String displayName = getMixfightDisplayName(player);
        
        // Send messages BEFORE teleportation
        PacketSendUtility.sendMessage(player,
            String.format("You have been eliminated! Final placement: %d/%d",
                placement, eliminatedPlayers.size() + participants.size()));
        announceElimination(displayName, placement);
        
        // Process removal and teleport
        removePlayerFromEvent(player, true);
        participants.remove(player.getObjectId());
        removeMixfightMasking(player);

        checkForEventEnd();
    }, 300); // 300ms delay to ensure resurrection completes
}

     public void eliminatePlayer(Player player) {
        if (!eventActive) {
            return;
        }

        MixfightParticipant participant = participants.get(player.getObjectId());
        if (participant == null) {
            return;
        }

        participant.incrementDeaths();
        int placement = participants.size() + eliminatedPlayers.size();
        eliminatedPlayers.put(player.getObjectId(), placement);
        
        String displayName = getMixfightDisplayName(player);
        
        // Send messages BEFORE teleportation
        PacketSendUtility.sendMessage(player,
            String.format("You have been eliminated! Final placement: %d/%d",
                placement, eliminatedPlayers.size() + participants.size()));
        announceElimination(displayName, placement);
        
        // Process removal and teleport
        removePlayerFromEvent(player, true);
        participants.remove(player.getObjectId());
        removeMixfightMasking(player);



        checkForEventEnd();
    }
    
    private void announceElimination(String displayName, int placement) {
        String message = String.format("%s has been eliminated! Placement: %d",
            displayName, placement);
            
        for (MixfightParticipant participant : participants.values()) {
            Player p = World.getInstance().getPlayer(participant.getPlayerId());
            if (p != null && p.isOnline()) {
                PacketSendUtility.sendMessage(p, message);
            }
        }
    }

    private void checkForEventEnd() {
        if (participants.size() <= 1) {
            endEventWithWinner();
        }
    }

    private void endEventWithWinner() {
        List<MixfightParticipant> finalRankings = new ArrayList<>();
        finalRankings.addAll(participants.values());

        List<Integer> eliminatedPlayerIds = new ArrayList<>();
        for (Map.Entry<Integer, Integer> entry : eliminatedPlayers.entrySet()) {
            eliminatedPlayerIds.add(entry.getKey());
        }

        eliminatedPlayerIds.sort((a, b) -> eliminatedPlayers.get(b).compareTo(eliminatedPlayers.get(a)));
        distributeBattleRoyaleRewards(finalRankings, eliminatedPlayerIds);
        endEvent();
    }

    private void distributeBattleRoyaleRewards(List<MixfightParticipant> winners, List<Integer> eliminatedPlayerIds) {
        giveParticipationRewards();
        giveTierRewards(winners, eliminatedPlayerIds);
    }

    private void giveParticipationRewards() {
        String[] parts = CustomConfig.MIXFIGHT_REWARDS_PARTICIPATION.split(":");
        if (parts.length != 4) return;

        try {
            int itemId = Integer.parseInt(parts[0]);
            int itemCount = Integer.parseInt(parts[1]);
            long kinah = Long.parseLong(parts[2]);

            Set<Integer> allPlayerIds = new HashSet<>();
            allPlayerIds.addAll(participants.keySet());
            allPlayerIds.addAll(eliminatedPlayers.keySet());

            for (Integer playerId : allPlayerIds) {
                Player player = World.getInstance().getPlayer(playerId);
                if (player != null) {
                    SystemMailService.sendMail("MixFight Event", player.getName(),
                        "MixFight Participation Reward", "Thank you for participating!",
                        itemId, itemCount, kinah, LetterType.EXPRESS);
                }
            }
        } catch (NumberFormatException e) {
            logError("Participation reward error: %s", e.getMessage());
        }
    }

    private void giveTierRewards(List<MixfightParticipant> winners, List<Integer> eliminatedPlayerIds) {
        List<Integer> finalRanking = new ArrayList<>();
        for (MixfightParticipant winner : winners) {
            finalRanking.add(winner.getPlayerId());
        }
        Collections.reverse(eliminatedPlayerIds);
        finalRanking.addAll(eliminatedPlayerIds);

        String[] tierConfigs = {
            CustomConfig.MIXFIGHT_REWARDS_TIER1,
            CustomConfig.MIXFIGHT_REWARDS_TIER2,
            CustomConfig.MIXFIGHT_REWARDS_TIER3,
            CustomConfig.MIXFIGHT_REWARDS_TIER4
        };

        for (int i = 0; i < Math.min(4, finalRanking.size()); i++) {
            Integer playerId = finalRanking.get(i);
            Player player = World.getInstance().getPlayer(playerId);
            if (player != null) {
                giveTierReward(player, i + 1, tierConfigs[i]);
            }
        }
    }

    private void giveTierReward(Player player, int placement, String rewardConfig) {
        String[] parts = rewardConfig.split(":");
        if (parts.length != 5) return;

        try {
            int itemId = Integer.parseInt(parts[1]);
            int itemCount = Integer.parseInt(parts[2]);
            long kinah = Long.parseLong(parts[3]);

            String placementText = getPlacementText(placement);
            SystemMailService.sendMail("MixFight Event", player.getName(),
                "MixFight " + placementText + " Place Reward",
                "Congratulations on achieving " + placementText + " place!",
                itemId, itemCount, kinah, LetterType.EXPRESS);
        } catch (NumberFormatException e) {
            logError("Tier reward error: %s", e.getMessage());
        }
    }

    private String getPlacementText(int placement) {
        switch (placement) {
            case 1: return "1st";
            case 2: return "2nd";
            case 3: return "3rd";
            case 4: return "4th";
            default: return placement + "th";
        }
    }

    public boolean isParticipating(Player player) {
        return participants.containsKey(player.getObjectId());
    }

    public boolean isEventActive() {
        return eventActive;
    }

    public boolean arePortalsActive() {
        return portalsActive;
    }

    public int getParticipantCount() {
        return participants.size();
    }

    public String getEventStatus() {
        if (eventActive) {
            long elapsed = (System.currentTimeMillis() - eventStartTime) / 1000 / 60;
            long remaining = CustomConfig.MIXFIGHT_DURATION_MINUTES - elapsed;
            return String.format("Event Active - %d participants, %d minutes remaining",
                participants.size(), Math.max(0, remaining));
        } else if (portalsActive) {
            long elapsed = (System.currentTimeMillis() - portalStartTime) / 1000 / 60;
            long remaining = CustomConfig.MIXFIGHT_PORTAL_DURATION_MINUTES - elapsed;
            return String.format("Portals Open - %d registered, event starts in %d minutes",
                participants.size(), Math.max(0, remaining));
        } else {
            return "Event Inactive";
        }
    }

    public boolean forceStartEvent() {
        if (eventActive) {
            return false;
        }
        startEventSequence();
        return true;
    }

    // Updated forceStopEvent method
public boolean forceStopEvent() {
    if (!eventActive && !portalsActive) {
        return false;
    }

    if (eventActive) {
        // Create a copy of participant IDs
        List<Integer> participantIds = new ArrayList<>(participants.keySet());
        
        for (int playerId : participantIds) {
            Player player = World.getInstance().getPlayer(playerId);
            if (player != null) {
                removePlayerFromEvent(player, true);
            }
        }

        // Schedule cleanup after all players have been processed
        ThreadPoolManager.getInstance().schedule(() -> {
            endEventWithCurrentStandings();
            cleanup();
            announceEventEnd();
        }, 11000); // 11 seconds to ensure 10s delay + buffer
        return true;
    } else {
        cleanup();
        return true;
    }
}
    private void endEventWithCurrentStandings() {
        List<MixfightParticipant> finalRankings = new ArrayList<>();
        finalRankings.addAll(participants.values());

        List<Integer> eliminatedPlayerIds = new ArrayList<>();
        for (Map.Entry<Integer, Integer> entry : eliminatedPlayers.entrySet()) {
            eliminatedPlayerIds.add(entry.getKey());
        }

        eliminatedPlayerIds.sort((a, b) -> eliminatedPlayers.get(b).compareTo(eliminatedPlayers.get(a)));
        distributeBattleRoyaleRewards(finalRankings, eliminatedPlayerIds);
        removeAllParticipantsAfterRewards();
        cleanup();
        announceEventEnd();
    }

    private void removeAllParticipantsAfterRewards() {
        participants.clear();
        eliminatedPlayers.clear();
        playerRegistrationLocations.clear();
        playerDeathTimes.clear();
    }

    private void teleportAllParticipantsOut() {
    if (currentEventInstance == null) {
        return;
    }

    for (MixfightParticipant participant : participants.values()) {
        Player player = World.getInstance().getPlayer(participant.getPlayerId());
        if (player != null && player.isOnline()) {
            // 1. Clear states IMMEDIATELY
            player.unsetCustomState(CustomPlayerState.MIXFIGHT_PARTICIPANT);
            player.unsetCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);
            
            // 2. Force attribute update BEFORE teleport
            player.getController().onChangedPlayerAttributes();
            
            // 3. Remove masking BEFORE teleport
            removeMixfightMasking(player);
            
            // 4. Teleport out if in event instance
            if (player.getWorldId() == currentEventMapId &&
                player.getInstanceId() == currentEventInstance.getInstanceId()) {
                teleportToRegistrationLocation(player);
            }
            
            // 5. Schedule post-teleport cleanup
            ThreadPoolManager.getInstance().schedule(() -> {
                if (player.isOnline()) {
                    // Final state confirmation
                    player.getController().onChangedPlayerAttributes();
                    
                    // Force knownlist refresh for player
                    player.clearKnownlist();
                    player.updateKnownlist();
                    
                    // Refresh nearby players' view
                    refreshNearbyPlayers(player);
                }
            }, 500);
        }
    }
}

// Helper method to refresh all participants' view of a specific player
    private void refreshEventParticipantsView(Player player) {
        if (player.getPosition() == null || player.getPosition().getWorldMapInstance() == null) {
            return;
        }
        WorldMapInstance instance = player.getPosition().getWorldMapInstance();
        for (Player p : instance.getPlayersInside()) {
            if (p != player) {
                p.getController().see(player);
            }
        }
    }
    
    // Add this method to reset knownlists for all participants
    private void refreshAllParticipantsKnownlist() {
        for (MixfightParticipant participant : participants.values()) {
            Player p = World.getInstance().getPlayer(participant.getPlayerId());
            if (p != null && p.isOnline()) {
                p.clearKnownlist();
                p.updateKnownlist();
            }
        }
    }

    // Helper method to refresh nearby players' view
    private void refreshNearbyPlayers(Player player) {
        if (player.getPosition() != null && player.getPosition().getWorldMapInstance() != null) {
            for (Player nearby : player.getPosition().getWorldMapInstance().getPlayersInside()) {
                if (PositionUtil.isInRange(player, nearby, 100)) {
                    nearby.getController().see(player);
                }
            }
        }
    }

    public MixfightParticipant getParticipant(Player player) {
        return participants.get(player.getObjectId());
    }

    // Reward tier enumeration
    private enum RewardTier {
        TIER1, TIER2, TIER3, TIER4, PARTICIPATION
    }

    // Participant class
    public static class MixfightParticipant {
        private final int playerId;
        private final String playerName;
        private int accumulatedAP = 0;
        private int kills = 0;
        private int deaths = 0;
        private long joinTime;

        public MixfightParticipant(int playerId, String playerName) {
            this.playerId = playerId;
            this.playerName = playerName;
            this.joinTime = System.currentTimeMillis();
        }

        public int getPlayerId() { return playerId; }
        public String getPlayerName() { return playerName; }
        public int getAccumulatedAP() { return accumulatedAP; }
        public void addAP(int ap) { this.accumulatedAP += ap; }
        public int getKills() { return kills; }
        public void incrementKills() { this.kills++; }
        public int getDeaths() { return deaths; }
        public void incrementDeaths() { this.deaths++; }
        public long getJoinTime() { return joinTime; }
    }
}