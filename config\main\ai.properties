#
# ----------------------------
# AI config's:
# ----------------------------

# Enable NPC movement
# Default: true
gameserver.npcmovement.enable = true

# The minimum time in seconds that the NPC waits before moving again.
# Default: 3 (seconds)
gameserver.npcmovement.delay.minimum = 3

# The maximum time in seconds that the NPC waits before moving again.
# Default: 15 (seconds)
gameserver.npcmovement.delay.maximum = 15

# Enable NPC shouts
# Default: false
gameserver.npcshouts.enable = false

# Location of AI *.java handlers
gameserver.ai.handler_directory = ./data/handlers/ai

# ----------------------------
# AI debug (developer) config's:
# ----------------------------

# Spawn AI NPCs with activated logging attribute
# Default: false
gameserver.ai.oncreate.debug = false

# Log movement for logging enabled NPCs
# Default: true
gameserver.ai.move.debug = true

# Save AI event log in NPC objects (can be retrieved via //ai2 command)
# Default: false
gameserver.ai.event.debug = false