#
# ----------------------------
# Geodata config's:
# ----------------------------

# Enable Geodata (heights and meshes)
# Default: true
gameserver.geodata.enable = true

# Enable canSee checks using geodata.
# Default: true
gameserver.geodata.cansee.enable = true

# Enable fear skill using geodata.
# Default: true
gameserver.geodata.fear.enable = true

# If enabled - during movements npcs will be checking z coordinate based on geo if target is flying
# Default: true
gameserver.geodata.npc.move = true

#Enable geo materials using skills
# Default: true
gameserver.geodata.materials.enable = true

# Show collision zone name and skill id for GMs
# Default: false
gameserver.geodata.materials.showdetails = false

# Enhanced position validation for skill effects and teleportation
# Default: true
gameserver.geodata.position.validation.enable = true

# Maximum Z-coordinate search range for ground detection (in meters)
# Default: 10.0
gameserver.geodata.position.validation.max_z_range = 10.0

# Minimum safe distance from collision surfaces (in meters)
# Default: 0.5
gameserver.geodata.position.validation.safe_distance = 0.5

# Enable fallback to original position if no safe ground is found
# Default: true
gameserver.geodata.position.validation.fallback_enabled = true

# Enable logging of position validation failures for debugging
# Default: false
gameserver.geodata.position.validation.debug_logging = false

# Enable geodata shields
# Default: true
gameserver.geodata.shields.enable = true