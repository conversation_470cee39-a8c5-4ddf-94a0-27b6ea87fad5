package com.aionemu.gameserver.services;
import com.aionemu.gameserver.model.team.group.PlayerGroupService;
import com.aionemu.gameserver.model.team.alliance.PlayerAllianceService;

import com.aionemu.gameserver.services.player.PlayerReviveService;

import java.util.concurrent.ScheduledFuture;
import com.aionemu.gameserver.network.aion.serverpackets.SM_SYSTEM_MESSAGE;
import com.aionemu.gameserver.network.aion.serverpackets.SM_EMOTION;
import com.aionemu.gameserver.model.EmotionType;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;
import java.util.ArrayList;

// Logging temporarily disabled for compilation
// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;

// import com.aionemu.commons.services.CronService;
// import com.aionemu.commons.utils.Rnd;
import com.aionemu.gameserver.configs.main.CustomConfig;
import com.aionemu.gameserver.dataholders.DataManager;
import com.aionemu.gameserver.model.ChatType;
import com.aionemu.gameserver.model.Race;
import com.aionemu.gameserver.model.gameobjects.Item;
import com.aionemu.gameserver.model.gameobjects.LetterType;
import com.aionemu.gameserver.model.gameobjects.player.CustomPlayerState;
import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.model.items.ItemSlot;
import com.aionemu.gameserver.model.templates.item.ItemTemplate;
import com.aionemu.gameserver.network.aion.serverpackets.SM_CUSTOM_SETTINGS;
import com.aionemu.gameserver.network.aion.serverpackets.SM_UPDATE_PLAYER_APPEARANCE;
import com.aionemu.gameserver.services.instance.InstanceService;
import com.aionemu.gameserver.services.mail.SystemMailService;
import com.aionemu.gameserver.services.teleport.TeleportService;
import com.aionemu.gameserver.utils.PacketSendUtility;
import com.aionemu.gameserver.utils.ThreadPoolManager;
import com.aionemu.gameserver.world.World;
import com.aionemu.gameserver.world.WorldMapInstance;


/**
 * FFA (Free-For-All) Event Service
 * 
 * Features:
 * - All players teleport to a single map where everyone can attack each other
 * - Same-race PvP enabled using ENEMY_OF_ALL_PLAYERS state
 * - Kill tracking and leaderboard system
 * - Reward distribution based on final ranking
 * - Admin and player command support
 * - Scheduled event activation
 * 
 * <AUTHOR> System
 */
public class FFAService {

    // private static final Logger log = LoggerFactory.getLogger(FFAService.class);
    private static final FFAService instance = new FFAService();

    // Random instance for map and coordinate selection
    private static final Random random = new Random();

    // Event state
    private final AtomicBoolean eventActive = new AtomicBoolean(false);
    
    // Participants management
    private final ConcurrentHashMap<Integer, FFAParticipant> participants = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<Integer, FFAParticipant> allEventParticipants = new ConcurrentHashMap<>();
	
	// Add new fields for timed announcements
    private ScheduledFuture<?> timedAnnouncementTask;
    private final Map<Long, Boolean> announcedTimes = new ConcurrentHashMap<>();

    // FFA Costume system - store original equipment appearance for restoration
    private final ConcurrentHashMap<Integer, List<Item>> originalEquipment = new ConcurrentHashMap<>();
    private static final int FFA_COSTUME_BODY_ID = 110900416; // FFA Body Costume
    private static final int FFA_COSTUME_HEAD_ID = 125045246; // FFA Head Costume

    // Death state tracking (playerId -> death timestamp)
    private final Map<Integer, Long> playerDeathTimes = new ConcurrentHashMap<>();

    // FFA Name system - store original names for restoration
    private final ConcurrentHashMap<Integer, String> originalNames = new ConcurrentHashMap<>();
    private static final String FFA_DISPLAY_NAME = "FFA-Challenger";
    
    // Event instance and timing
    private WorldMapInstance eventInstance;
    private long eventStartTime = 0;
    private Future<?> eventTask;
    private Future<?> announcementTask;
    private Future<?> deathMonitorTask;

    // Map selection
    private List<Integer> availableMaps;
    private int currentEventMapId;

    // Spawn coordinates (legacy support)
    private float spawnX;
    private float spawnY;
    private float spawnZ;
    private byte spawnHeading;

    private FFAService() {
        parseAvailableMaps();
        parseSpawnCoordinates();
    }

    public static FFAService getInstance() {
        return instance;
    }

    /**
     * Initialize the FFA service
     */
    public void initialize() {
        if (!CustomConfig.FFA_ENABLED) {
            // System.out.println("FFA system is disabled in configuration");
            return;
        }

        // System.out.println("Initializing FFA service...");

        // Schedule automatic start if configured
        if (CustomConfig.FFA_AUTO_START) {
            if (!CustomConfig.FFA_SCHEDULE.isEmpty()) {
                // TODO: Implement cron scheduling when commons library is available
                // For now, just start immediately if auto-start is enabled
                // System.out.println("FFA cron scheduling not available, starting immediately instead");
                startEvent();
            } else {
                // Start immediately if auto-start is enabled but no schedule
                // System.out.println("Auto-starting FFA event immediately");
                startEvent();
            }
        } else {
            // System.out.println("FFA system requires manual start via admin command //ffa start");
        }

        // System.out.println("FFA service initialized successfully!");
    }

    /**
     * Parse spawn coordinates from configuration
     */
    private void parseSpawnCoordinates() {
        try {
            String[] coords = CustomConfig.FFA_SPAWN_COORDS.split(",");
            if (coords.length >= 3) {
                spawnX = Float.parseFloat(coords[0].trim());
                spawnY = Float.parseFloat(coords[1].trim());
                spawnZ = Float.parseFloat(coords[2].trim());
                spawnHeading = coords.length > 3 ? Byte.parseByte(coords[3].trim()) : 0;
            } else {
                // Default coordinates for Dark Poeta
                spawnX = 242.52405f;
                spawnY = 424.71637f;
                spawnZ = 103.80612f;
                spawnHeading = 0;
            }
        } catch (Exception e) {
            // System.out.println("Failed to parse FFA spawn coordinates, using defaults: " + e.getMessage());
            spawnX = 242.52405f;
            spawnY = 424.71637f;
            spawnZ = 103.80612f;
            spawnHeading = 0;
        }
    }

    /**
     * Parse available maps from configuration
     */
    private void parseAvailableMaps() {
        availableMaps = new ArrayList<>();
        try {
            String[] maps = CustomConfig.FFA_MAPS.split(",");
            for (String mapStr : maps) {
                int mapId = Integer.parseInt(mapStr.trim());
                availableMaps.add(mapId);
            }
            // System.out.println("Parsed " + availableMaps.size() + " available FFA maps: " + availableMaps);
        } catch (Exception e) {
            // System.out.println("Failed to parse FFA maps, using default Dark Poeta: " + e.getMessage());
            availableMaps.add(300040000); // Default to Dark Poeta
        }

        if (availableMaps.isEmpty()) {
            availableMaps.add(300040000); // Fallback to Dark Poeta
        }
    }

    /**
     * Select a random event map from available maps
     */
    private int selectRandomEventMap() {
        if (availableMaps.isEmpty()) {
            return 300040000; // Default to Dark Poeta
        }
        return availableMaps.get(random.nextInt(availableMaps.size()));
    }

    /**
     * Get random spawn coordinates for the specified map
     * Returns [x, y, z, heading]
     */
    private float[] getRandomSpawnCoordinates(int mapId) {
        List<float[]> coords = parseSpawnCoordinatesForMap(mapId);

        if (coords.isEmpty()) {
            // Fallback to legacy spawn coordinates
            return new float[]{spawnX, spawnY, spawnZ, spawnHeading};
        }

        // Return random coordinates from the list
        return coords.get(random.nextInt(coords.size()));
    }

    /**
     * Parse spawn coordinates for a specific map from configuration
     */
    private List<float[]> parseSpawnCoordinatesForMap(int mapId) {
        List<float[]> coords = new ArrayList<>();

        try {
            String coordsConfig = null;

            // Get map-specific coordinates from configuration
            switch (mapId) {
                case 300030000: // Nochsana
                    coordsConfig = CustomConfig.FFA_SPAWN_300030000;
                    break;
                case 300040000: // Dark Poeta
                    coordsConfig = CustomConfig.FFA_SPAWN_300040000;
                    break;
                case 300250000: // Esoterrace
                    coordsConfig = CustomConfig.FFA_SPAWN_300250000;
                    break;
                default:
                    System.out.println("No specific spawn coordinates configured for map " + mapId + ", using fallback");
                    return coords; // Return empty list to trigger fallback
            }

            if (coordsConfig != null && !coordsConfig.trim().isEmpty()) {
                // Parse coordinates: format is "x,y,z,h;x,y,z,h;..."
                String[] coordSets = coordsConfig.split(";");
                for (String coordSet : coordSets) {
                    String[] parts = coordSet.trim().split(",");
                    if (parts.length >= 4) {
                        float x = Float.parseFloat(parts[0].trim());
                        float y = Float.parseFloat(parts[1].trim());
                        float z = Float.parseFloat(parts[2].trim());
                        float h = Float.parseFloat(parts[3].trim());
                        coords.add(new float[]{x, y, z, h});
                    }
                }
            }

            System.out.println("Loaded " + coords.size() + " spawn coordinates for map " + mapId);

        } catch (Exception e) {
            System.err.println("Error parsing spawn coordinates for map " + mapId + ": " + e.getMessage());
        }

        return coords;
    }

    /**
     * Start the FFA event
     */
    public void startEvent() {
        if (!CustomConfig.FFA_ENABLED) {
            // System.out.println("FFA system is disabled in configuration");
            return;
        }

        if (eventActive.get()) {
            // System.out.println("FFA event is already active");
            return;
        }

        // Select random map for this event
        currentEventMapId = selectRandomEventMap();
        // System.out.println("Starting FFA event on randomly selected map: " + currentEventMapId);

        try {
            // Create event instance using the randomly selected map - only create ONE instance for all players
            if (eventInstance == null) {
                eventInstance = InstanceService.getNextAvailableInstance(currentEventMapId, 0, (byte) 0,
                    CustomConfig.FFA_MAX_PLAYERS, true);
                if (eventInstance == null) {
                    // System.out.println("Failed to create FFA event instance for map " + currentEventMapId + " - instance creation returned null");
                    // System.out.println("Make sure map " + currentEventMapId + " is a valid instance map that supports instancing");
                    return;
                }

                // System.out.println("Created FFA event instance " + eventInstance.getInstanceId() + " for map " + currentEventMapId + " - all players will join this instance");

                // Clear all monsters from the instance to create a clean PvP environment
                // Add a small delay to ensure instance is fully loaded before clearing
                ThreadPoolManager.getInstance().schedule(() -> {
                    clearInstanceMonsters();
                }, 2000); // 2 second delay
            } else {
                // System.out.println("Reusing existing FFA event instance " + eventInstance.getInstanceId() + " for map " + currentEventMapId);
            }

        } catch (Exception e) {
            // System.out.println("Exception occurred while creating FFA event instance for map " + currentEventMapId + ": " + e.getMessage());
            // System.out.println("Make sure map " + currentEventMapId + " is a valid instance map (like 300020000, 300030000, 300040000)");
            return;
        }

        eventActive.set(true);
        eventStartTime = System.currentTimeMillis();
		
		// Start timed announcements
        startTimedAnnouncements();

        // Reset all kill scores for new event (fresh start)
        allEventParticipants.clear();
        // System.out.println("FFA: Cleared all previous kill scores for fresh event start");

        // Announce event start
        announceEventStart();

        // Schedule event end
        eventTask = ThreadPoolManager.getInstance().schedule(() -> {
            endEvent();
        }, CustomConfig.FFA_DURATION_MINUTES * 60 * 1000);

        // Start death state monitoring (every 1 second for immediate revival, same as OneVsOne)
        deathMonitorTask = ThreadPoolManager.getInstance().scheduleAtFixedRate(() -> {
            monitorDeathStates();
        }, 1000, 1000);

        // System.out.println("FFA event started on map " + currentEventMapId + " with instance " + eventInstance.getInstanceId() + " with death monitoring active");
    }
	/**
     * Start timed announcements for event duration
     */
    private void startTimedAnnouncements() {
        final long totalDurationMs = CustomConfig.FFA_DURATION_MINUTES * 60 * 1000;
        final long startTime = System.currentTimeMillis();
        
        timedAnnouncementTask = ThreadPoolManager.getInstance().scheduleAtFixedRate(() -> {
            if (!eventActive.get()) return;
            
            long elapsedMs = System.currentTimeMillis() - startTime;
            long remainingMs = totalDurationMs - elapsedMs;
            
            // Event has ended
            if (remainingMs <= 0) {
                return;
            }
            
            // Convert to seconds and minutes
            long remainingSec = remainingMs / 1000;
            long remainingMin = remainingSec / 60;
            long lastMinSec = remainingSec % 60;
            
            // Phase 3: Last 10 seconds - announce every second
            if (remainingSec <= 10) {
                if (!announcedTimes.containsKey(remainingSec)) {
                    announceToParticipants(remainingSec + " seconds remaining!");
                    announcedTimes.put(remainingSec, true);
                }
                return;
            }
            
            // Phase 2: Last 5 minutes - announce every minute
            if (remainingMin < 5) {
                // Announce at exact minute marks (when seconds are 0)
                if (lastMinSec == 0 && !announcedTimes.containsKey(remainingMin)) {
                    announceToParticipants(remainingMin + " minutes remaining!");
                    announcedTimes.put(remainingMin, true);
                }
                return;
            }
            
            // Phase 1: Regular phase - announce every 10 minutes
            if (remainingMin % 10 == 0 && lastMinSec == 0) {
                if (!announcedTimes.containsKey(remainingMin)) {
                    announceToParticipants(remainingMin + " minutes remaining!");
                    announcedTimes.put(remainingMin, true);
                }
            }
        }, 0, 1000); // Check every second
    }

    /**
     * Announce message to all participants
     */
    private void announceToParticipants(String message) {
        for (FFAParticipant participant : participants.values()) {
            Player player = World.getInstance().getPlayer(participant.getPlayerId());
            if (player != null && player.isOnline()) {
                PacketSendUtility.sendMessage(player, message, ChatType.BRIGHT_YELLOW_CENTER);
            }
        }
        System.out.println("FFA announcement: " + message);
    }

    /**
     * End the FFA event
     */
    public void endEvent() {
        if (!eventActive.get()) {
            return;
        }

        // System.out.println("Ending FFA event");

        // Calculate and distribute rewards
        distributeRewards();

        // Announce winners to server
        announceWinners();

        // Remove all participants from event
        removeAllParticipants();

        // Cleanup
        cleanup();

        // Announce event end
        announceEventEnd();
    }

    /**
     * Force start event (admin command)
     */
    public void forceStartEvent() {
        if (!eventActive.get()) {
            // System.out.println("Force starting FFA event");
            startEvent();
        }
    }

    /**
     * Force end event (admin command)
     */
    public void forceEndEvent() {
        if (eventActive.get()) {
            // System.out.println("Force ending FFA event");
            endEvent();
        }
    }

    /**
     * Cleanup event resources
     */
    private void cleanup() {
        eventActive.set(false);
        eventStartTime = 0;

        if (eventTask != null && !eventTask.isCancelled()) {
            eventTask.cancel(true);
            eventTask = null;
        }

        if (announcementTask != null && !announcementTask.isCancelled()) {
            announcementTask.cancel(true);
            announcementTask = null;
        }

        if (deathMonitorTask != null && !deathMonitorTask.isCancelled()) {
            deathMonitorTask.cancel(true);
            deathMonitorTask = null;
        }

        if (eventInstance != null) {
            // Instance cleanup will be handled by the system
            eventInstance = null;
        }
		// Cancel timed announcements
        if (timedAnnouncementTask != null && !timedAnnouncementTask.isCancelled()) {
            timedAnnouncementTask.cancel(true);
            timedAnnouncementTask = null;
        }
        announcedTimes.clear();

        // Clear only active participants (allEventParticipants cleared at event start for fresh scores)
        participants.clear();
        // Note: allEventParticipants is cleared at event start to reset scores between events

        // Clear costume data
        originalEquipment.clear();

        // Clear death tracking
        playerDeathTimes.clear();

        // Clear name data
        originalNames.clear();

        // System.out.println("FFA event cleanup completed - preserved kill statistics for leaderboard");
    }

    /**
     * Announce event start
     */
    private void announceEventStart() {
        if (CustomConfig.FFA_ANNOUNCEMENTS) {
            String message = "FFA Event has started! Use .ffa to join the battle!";
            World.getInstance().getAllPlayers().forEach(player -> {
                if (player.getLevel() >= CustomConfig.FFA_MIN_LEVEL) {
                    PacketSendUtility.sendMessage(player, message, ChatType.BRIGHT_YELLOW_CENTER);
                }
            });
        }
    }

    /**
     * Announce event end
     */
    private void announceEventEnd() {
        if (CustomConfig.FFA_ANNOUNCEMENTS) {
            String message = "FFA Event has ended! Check your mail for rewards.";
            World.getInstance().getAllPlayers().forEach(player -> {
                PacketSendUtility.sendMessage(player, message, ChatType.BRIGHT_YELLOW_CENTER);
            });
        }
    }

    /**
     * Announce the top 3 winners to the server
     */
    private void announceWinners() {
        if (!CustomConfig.FFA_ANNOUNCEMENTS || allEventParticipants.isEmpty()) {
            return;
        }

        // Get top 3 participants
        List<FFAParticipant> topParticipants = allEventParticipants.values().stream()
            .sorted((p1, p2) -> Integer.compare(p2.getKills(), p1.getKills()))
            .limit(3)
            .collect(Collectors.toList());

        if (topParticipants.isEmpty()) {
            return;
        }

        // Announce winners
        String winnerMessage = "=== FFA Event Winners ===";
        World.getInstance().getAllPlayers().forEach(player -> {
            PacketSendUtility.sendMessage(player, winnerMessage, ChatType.BRIGHT_YELLOW_CENTER);
        });

        // Announce each winner with a delay
        for (int i = 0; i < topParticipants.size(); i++) {
            FFAParticipant participant = topParticipants.get(i);
            String place = (i == 0) ? "1st" : (i == 1) ? "2nd" : "3rd";
            String message = String.format("%s Place: %s with %d kills!",
                place, participant.getPlayerName(), participant.getKills());

            final int delay = (i + 1) * 2000; // 2 second delay between announcements
            ThreadPoolManager.getInstance().schedule(() -> {
                World.getInstance().getAllPlayers().forEach(player -> {
                    PacketSendUtility.sendMessage(player, message, ChatType.BRIGHT_YELLOW_CENTER);
                });
            }, delay);
        }
    }

    /**
     * Check if player can join FFA event
     */
    public boolean canJoinEvent(Player player) {
        if (!CustomConfig.FFA_ENABLED || !eventActive.get()) {
            return false;
        }

        if (player.getLevel() < CustomConfig.FFA_MIN_LEVEL) {
            return false;
        }

        if (participants.size() >= CustomConfig.FFA_MAX_PLAYERS) {
            return false;
        }

        // Check if player is already actively participating (not eliminated)
        if (participants.containsKey(player.getObjectId())) {
            return false;
        }

        // Players can re-join even if they participated before (were eliminated)
        return true;
    }

    /**
     * Add player to FFA event
     */
    public boolean joinEvent(Player player) {
        if (!canJoinEvent(player)) {
            return false;
        }

        // System.out.println("Player " + player.getName() + " joining FFA event");

        // Check if player has participated before (preserve kill count)
        FFAParticipant participant = allEventParticipants.get(player.getObjectId());
        if (participant == null) {
            // New participant
            participant = new FFAParticipant(player.getObjectId(), player.getName());
            allEventParticipants.put(player.getObjectId(), participant);
        } else {
            // Returning participant - reset join time but preserve kills
            participant.rejoin();
        }

        // Add to active participants
        participants.put(player.getObjectId(), participant);
		

        // Debug: Verify participant is in both maps (can be removed once stable)
        // System.out.println("DEBUG: Added participant " + player.getName() + " to both maps");

        // Set FFA participant state for same-race PvP - ALL players are enemies
        player.setCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);
        player.getController().onChangedPlayerAttributes();
        // System.out.println("DEBUG: Set ENEMY_OF_ALL_PLAYERS state for " + player.getName());

        // Ensure all existing participants see this player as enemy and vice versa
        updateAllPlayersEnemyState();

        // Remove any teleportation protection after a short delay
        ThreadPoolManager.getInstance().schedule(() -> {
            if (player.isOnline() && participants.containsKey(player.getObjectId())) {
                player.unsetCustomState(CustomPlayerState.INVULNERABLE);
                player.unsetCustomState(CustomPlayerState.TELEPORTATION_MODE);
                player.getController().onChangedPlayerAttributes();

                // Re-ensure enemy state after teleportation protection is removed
                player.setCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);
                player.getController().onChangedPlayerAttributes();
                // System.out.println("Confirmed ENEMY_OF_ALL_PLAYERS state for player " + player.getName() + " after teleportation protection");
            }
        }, 3000); // 3 second delay

        // Apply FFA costume
        // System.out.println("Applying FFA costume to player: " + player.getName());
        applyFFACostume(player);

        // Apply FFA display name
        applyFFAName(player);

        // Teleport to event map
        if (eventInstance != null) {
            teleportToEventMap(player);
        }

        PacketSendUtility.sendMessage(player, "You have joined the FFA Event! Fight for glory!",
            ChatType.BRIGHT_YELLOW_CENTER);

        // System.out.println("Player " + player.getName() + " joined FFA event");
        return true;
    }

    /**
     * Remove player from FFA event
     */
    public boolean leaveEvent(Player player) {
        FFAParticipant participant = participants.get(player.getObjectId());
        if (participant == null) {
            return false;
        }

        removeParticipant(player, true);
        return true;
    }

    /**
     * Remove participant from FFA event (for external services like disconnect handling)
     */
    public void removeParticipant(Player player) {
        if (participants.containsKey(player.getObjectId())) {
            removeParticipant(player, false); // false = involuntary (disconnect)
        }
    }

    /**
     * Handle player disconnect during FFA event
     * This ensures proper cleanup of FFA states when players disconnect
     */
    public void handlePlayerDisconnect(Player player) {
        try {
            // System.out.println("Handling potential FFA disconnect for player: " + player.getName());

            // Always check for FFA name cleanup, regardless of event state
            // This handles cases where event ended but player still has FFA name
            if (originalNames.containsKey(player.getObjectId())) {
                // System.out.println("Cleaning up FFA name for disconnected player: " + player.getName() + " (had FFA name)");
                removeFFAName(player);
            }

            // If event is not active, check for lingering FFA states and clean them up
            if (!eventActive.get()) {
                // Even if event is not active, clean up any lingering FFA states
                // This handles edge cases where states weren't properly cleaned up
                if (player.isInCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS)) {
                    // System.out.println("Cleaning up lingering FFA ENEMY_OF_ALL_PLAYERS state for disconnected player: " + player.getName());
                    player.unsetCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);
                    player.getController().onChangedPlayerAttributes();
                }
                return;
            }

            FFAParticipant participant = participants.get(player.getObjectId());
            if (participant == null) {
                // Even if not in participants list, clean up any lingering FFA states
                // This handles edge cases where states weren't properly cleaned up
                if (player.isInCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS)) {
                    // System.out.println("Cleaning up lingering FFA states for disconnected player not in participants: " + player.getName());
                    player.unsetCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);
                    player.getController().onChangedPlayerAttributes();
                }
                return;
            }

            // System.out.println("Handling FFA disconnect for active participant: " + player.getName());

            // Remove from active participants (preserve kill count in allEventParticipants)
            participants.remove(player.getObjectId());

            // Clean up FFA states
            removeFFACostume(player);
            // removeFFAName already called above to handle all cases

            // Remove FFA participant state
            player.unsetCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);
            player.getController().onChangedPlayerAttributes();

            // Clean up death tracking
            playerDeathTimes.remove(player.getObjectId());

            // System.out.println("Cleaned up FFA states for disconnected player: " + player.getName());

        } catch (Exception e) {
            System.err.println("Error handling FFA disconnect for player " + player.getName() + ": " + e.getMessage());

            // Emergency name cleanup - try to restore name even if other cleanup fails
            try {
                if (originalNames.containsKey(player.getObjectId())) {
                    System.err.println("Emergency FFA name cleanup for player: " + player.getName());
                    removeFFAName(player);
                }
            } catch (Exception nameError) {
                System.err.println("Emergency FFA name cleanup failed for player " + player.getName() + ": " + nameError.getMessage());
            }
        }
    }

    /**
     * Remove participant from event
     */
    private void removeParticipant(Player player, boolean voluntary) {
    participants.remove(player.getObjectId());
    
    // Clear states
    player.unsetCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);
    player.getController().onChangedPlayerAttributes();
    
    // Refresh knownlist for remaining participants
    refreshAllPlayersKnownlist();
    
    // Remove FFA costume
    removeFFACostume(player);

    // Remove FFA display name
    removeFFAName(player);

    // Teleport out of event instance if still inside
    if (eventInstance != null && player.getInstanceId() == eventInstance.getInstanceId()) {
        teleportToCapitalCity(player);
        
        // Add delayed attribute update HERE
        ThreadPoolManager.getInstance().schedule(() -> {
            if (player.isOnline()) {
                player.getController().onChangedPlayerAttributes();
            }
        }, 1000);
    }

    String message = voluntary ? "You have left the FFA Event." : "You have been removed from the FFA Event.";
    PacketSendUtility.sendMessage(player, message, ChatType.BRIGHT_YELLOW_CENTER);
}

    /**
     * Remove all participants from event
     */
    private void removeAllParticipants() {
        List<Player> playersToRemove = new ArrayList<>();

        for (FFAParticipant participant : participants.values()) {
            Player player = World.getInstance().getPlayer(participant.getPlayerId());
            if (player != null && player.isOnline()) {
                playersToRemove.add(player);
            }
        }

        for (Player player : playersToRemove) {
            removeParticipant(player, false);
        }

        // Safety check: Clean up any remaining FFA names from players who might not be in participants list
        // This handles edge cases where players have FFA names but aren't in the active participants
        cleanupAllFFANames();
    }

    /**
     * Safety method to clean up any remaining FFA names from all online players
     * This is called when the event ends to ensure no player is left with a stuck FFA name
     */
    private void cleanupAllFFANames() {
        try {
            // System.out.println("Performing safety cleanup of all FFA names");

            // Create a copy of the originalNames map to avoid concurrent modification
            Map<Integer, String> namesToCleanup = new HashMap<>(originalNames);

            for (Map.Entry<Integer, String> entry : namesToCleanup.entrySet()) {
                Integer playerId = entry.getKey();
                Player player = World.getInstance().getPlayer(playerId);

                if (player != null && player.isOnline()) {
                    if (FFA_DISPLAY_NAME.equals(player.getName())) {
                        // System.out.println("Safety cleanup: Restoring FFA name for player " + playerId);
                        removeFFAName(player);
                    } else {
                        // Player doesn't have FFA name anymore, remove from tracking
                        originalNames.remove(playerId);
                    }
                } else {
                    // Player is offline, remove from tracking (name will be restored on login if needed)
                    originalNames.remove(playerId);
                }
            }

            // System.out.println("FFA name safety cleanup completed");

        } catch (Exception e) {
            System.err.println("Error during FFA name safety cleanup: " + e.getMessage());
        }
    }

    /**
     * Teleport player to event map
     */
    private void teleportToEventMap(Player player) {
        if (eventInstance == null) {
            // System.out.println("Cannot teleport player " + player.getName() + " to FFA event - no event instance");
            return;
        }

        try {
            // Get random spawn coordinates for the current event map
            float[] coords = getRandomSpawnCoordinates(currentEventMapId);

            // Teleport to the event instance with random coordinates
            TeleportService.teleportTo(player, currentEventMapId, eventInstance.getInstanceId(),
                coords[0], coords[1], coords[2], (byte) coords[3]);

            // System.out.println("Teleported player " + player.getName() + " to FFA event at coordinates [" + coords[0] + ", " + coords[1] + ", " + coords[2] + "] on map " + currentEventMapId + " instance " + eventInstance.getInstanceId());
        } catch (Exception e) {
            // System.out.println("Error teleporting player " + player.getName() + " to FFA event: " + e.getMessage());
        }
		// Remove player from any groups or alliances
    if (player.isInGroup()) {  // Changed to isInGroup()
        PlayerGroupService.removePlayer(player);
        PacketSendUtility.sendMessage(player, "You have been removed from your group for the FFA event", 
            ChatType.BRIGHT_YELLOW_CENTER);
    }
    if (player.isInAlliance()) {  // Changed to isInAlliance()
        PlayerAllianceService.removePlayer(player);
        PacketSendUtility.sendMessage(player, "You have been removed from your alliance for the FFA event", 
            ChatType.BRIGHT_YELLOW_CENTER);
    }
    }
	private void refreshAllPlayersKnownlist() {
    for (FFAParticipant participant : participants.values()) {
        Player player = World.getInstance().getPlayer(participant.getPlayerId());
        if (player != null && player.isOnline()) {
            player.clearKnownlist();
            player.updateKnownlist();
        }
    }
}

    /**
     * Respawn player in FFA event using custom.properties coordinates
     * Called by instance handlers when players die in FFA
     */
    public void respawnPlayerInEvent(Player player) {
        if (!isParticipant(player) || eventInstance == null) {
            return;
        }

        try {
            // Get random spawn coordinates for the current event map from custom.properties
            float[] coords = getRandomSpawnCoordinates(currentEventMapId);

            // Teleport to the event instance with random coordinates
            TeleportService.teleportTo(player, eventInstance, coords[0], coords[1], coords[2], (byte) coords[3]);

            System.out.println("FFA Respawn: Teleported " + player.getName() + " to custom.properties coordinates [" +
                coords[0] + ", " + coords[1] + ", " + coords[2] + "] on map " + currentEventMapId);
        } catch (Exception e) {
            System.out.println("Error respawning player " + player.getName() + " in FFA event: " + e.getMessage());
        }
    }

    /**
     * Teleport player to capital city
     */
    private void teleportToCapitalCity(Player player) {
        try {
            String teleportConfig;
            if (player.getRace() == Race.ELYOS) {
                teleportConfig = CustomConfig.FFA_TELEPORT_ELYOS;
            } else {
                teleportConfig = CustomConfig.FFA_TELEPORT_ASMODIANS;
            }

            // Parse teleport configuration: mapId,x,y,z,heading
            String[] parts = teleportConfig.split(",");
            if (parts.length != 5) {
                System.err.println("Invalid FFA teleport configuration: " + teleportConfig);
                return;
            }

            int mapId = Integer.parseInt(parts[0]);
            float x = Float.parseFloat(parts[1]);
            float y = Float.parseFloat(parts[2]);
            float z = Float.parseFloat(parts[3]);
            byte heading = Byte.parseByte(parts[4]);

            TeleportService.teleportTo(player, mapId, x, y, z, heading);
            System.out.println("Teleported player " + player.getName() + " back to capital city (Map: " + mapId + ")");
        } catch (Exception e) {
            System.err.println("Failed to teleport player " + player.getName() + " to capital city: " + e.getMessage());
        }
    }

    /**
     * Handle player kill in FFA event
     */
    public void onPlayerKill(Player killer, Player victim) {
        if (!eventActive.get()) {
            System.out.println("FFA kill ignored - event not active");
            return;
        }

        FFAParticipant killerParticipant = participants.get(killer.getObjectId());
        FFAParticipant victimParticipant = participants.get(victim.getObjectId());

        if (killerParticipant == null) {
            System.out.println("FFA kill ignored - killer " + killer.getName() + " not in participants map");
            return;
        }

        if (victimParticipant == null) {
            System.out.println("FFA kill ignored - victim " + victim.getName() + " not in participants map");
            return;
        }

        // Increment killer's kill count (both maps contain the same participant object)
        int oldKills = killerParticipant.getKills();
        killerParticipant.incrementKills();
        int newKills = killerParticipant.getKills();

        System.out.println("FFA Kill tracked: " + killer.getName() + " killed " + victim.getName() +
            " (Kills: " + oldKills + " -> " + newKills + ")");

        // Verify the kill was recorded in allEventParticipants as well
        FFAParticipant allParticipantsEntry = allEventParticipants.get(killer.getObjectId());
        if (allParticipantsEntry != null) {
            System.out.println("Verified kill in allEventParticipants: " + killer.getName() +
                " has " + allParticipantsEntry.getKills() + " kills");
        } else {
            System.err.println("ERROR: Killer " + killer.getName() + " not found in allEventParticipants map!");
        }

        // Remove death tracking for victim (they will be handled by death monitor if needed)
        playerDeathTimes.remove(victim.getObjectId());

        // Announce kill using FFA display names
        String killerDisplayName = getFFADisplayName(killer);
        String victimDisplayName = getFFADisplayName(victim);
        String killMessage = String.format("%s eliminated %s! (Kills: %d)",
            killerDisplayName, victimDisplayName, killerParticipant.getKills());

        // Send kill message to all FFA participants
        for (FFAParticipant participant : participants.values()) {
            Player player = World.getInstance().getPlayer(participant.getPlayerId());
            if (player != null && player.isOnline()) {
                PacketSendUtility.sendMessage(player, killMessage, ChatType.BRIGHT_YELLOW_CENTER);
            }
        }

        System.out.println("FFA Kill announcement sent: " + killMessage);
    }

    /**
     * Clear all monsters from the event instance
     */
    private void clearInstanceMonsters() {
        if (eventInstance == null) {
            return;
        }

        try {
            // Get all NPCs in the instance and remove them
            eventInstance.getNpcs().forEach(npc -> {
                if (npc != null && !npc.isDead()) {
                    npc.getController().delete();
                }
            });

            // System.out.println("Cleared all monsters from FFA event instance " + eventInstance.getInstanceId());
        } catch (Exception e) {
            // System.out.println("Error clearing monsters from FFA event instance: " + e.getMessage());
        }
    }

    /**
     * Apply FFA costume to player
     */
    private void applyFFACostume(Player player) {
        try {
            // Store original equipment for restoration
            List<Item> originalItems = new ArrayList<>(player.getEquipment().getEquippedForAppearance());
            originalEquipment.put(player.getObjectId(), originalItems);

            // Apply costumes directly using the same logic as Preview command but without chat messages
            applyCostumeSilent(player);

            // Schedule a task to keep reapplying the costume every 8 seconds to keep it persistent
            // This ensures the costume stays active throughout the FFA event
            ThreadPoolManager.getInstance().scheduleAtFixedRate(() -> {
                if (participants.containsKey(player.getObjectId()) && player.isOnline()) {
                    // Reapply costume silently
                    applyCostumeSilent(player);
                }
            }, 8000, 8000); // Every 8 seconds (before the 10-second reset)

        } catch (Exception e) {
            System.err.println("Failed to apply FFA costume to player " + player.getName() + ": " + e.getMessage());
        }
    }

    /**
     * Apply costume silently without chat messages (based on Preview command logic)
     */
    private void applyCostumeSilent(Player player) {
        try {
            // Get item templates for both costumes
            ItemTemplate bodyTemplate = DataManager.ITEM_DATA.getItemTemplate(FFA_COSTUME_BODY_ID);
            ItemTemplate headTemplate = DataManager.ITEM_DATA.getItemTemplate(FFA_COSTUME_HEAD_ID);

            if (bodyTemplate == null || headTemplate == null) {
                System.err.println("Failed to find costume templates - Body: " + (bodyTemplate != null) + ", Head: " + (headTemplate != null));
                return;
            }

            // Create preview items list exactly like Preview command
            List<Item> previewItems = new ArrayList<>();
            long previewItemsSlotMask = 0;

            // Add body costume using Preview command's addFakeItem logic
            long bodySlotMask = bodyTemplate.getItemSlot();
            if (ItemSlot.isVisible(bodySlotMask)) {
                Item bodyItem = new Item(0, bodyTemplate, 1, true, bodySlotMask);
                previewItems.add(bodyItem);
                previewItemsSlotMask |= bodySlotMask;
            }

            // Add head costume using Preview command's addFakeItem logic
            long headSlotMask = headTemplate.getItemSlot();
            if (ItemSlot.isVisible(headSlotMask)) {
                Item headItem = new Item(0, headTemplate, 1, true, headSlotMask);
                previewItems.add(headItem);
                previewItemsSlotMask |= headSlotMask;
            }

            // Add player's existing equipment (except for slots occupied by costumes) - exactly like Preview command
            for (Item visibleEquipment : player.getEquipment().getEquippedForAppearance()) {
                if ((visibleEquipment.getEquipmentSlot() & previewItemsSlotMask) == 0) {
                    previewItems.add(visibleEquipment);
                }
            }

            // Sort items by equipment slot to avoid display bugs - exactly like Preview command
            previewItems.sort(java.util.Comparator.comparingLong(Item::getEquipmentSlot));

            // Apply costume appearance - exactly like Preview command
            int display = player.getPlayerSettings().getDisplay() | SM_CUSTOM_SETTINGS.HIDE_LEGION_CLOAK;
            if (previewItems.stream().anyMatch(item -> item.getEquipmentSlot() == ItemSlot.HELMET.getSlotIdMask())) {
                display &= ~SM_CUSTOM_SETTINGS.HIDE_HELMET;
            }
            if (previewItems.stream().anyMatch(item -> item.getEquipmentSlot() == ItemSlot.PLUME.getSlotIdMask())) {
                display &= ~SM_CUSTOM_SETTINGS.HIDE_PLUME;
            }

            // Send appearance update packets (no chat messages) - exactly like Preview command
            PacketSendUtility.sendPacket(player, new SM_CUSTOM_SETTINGS(player.getObjectId(), 1, display, player.getPlayerSettings().getDeny()));
            PacketSendUtility.sendPacket(player, new SM_UPDATE_PLAYER_APPEARANCE(player.getObjectId(), previewItems));

            // IMPORTANT: Also broadcast the appearance update to all nearby players so they can see the costume
            PacketSendUtility.broadcastPacket(player, new SM_UPDATE_PLAYER_APPEARANCE(player.getObjectId(), previewItems), false);

        } catch (Exception e) {
            System.err.println("Failed to apply FFA costume silently to player " + player.getName() + ": " + e.getMessage());
        }
    }

    /**
     * Check and restore player name if they have a stuck FFA name from previous session
     * Called when player logs in to ensure proper name restoration
     * NOTE: Since we no longer change database names, this should rarely be needed
     */
    public void checkAndRestorePlayerName(Player player) {
        try {
            // Since we no longer change the actual database name, players shouldn't have stuck FFA names
            // But we'll keep this as a safety check for any legacy issues

            // Clean up any FFA name tracking for this player if they're not in an active event
            if (originalNames.containsKey(player.getObjectId()) && !eventActive.get()) {
                originalNames.remove(player.getObjectId());
                // System.out.println("Cleaned up FFA name tracking for player " + player.getName() + " on login");
            }

        } catch (Exception e) {
            System.err.println("Error checking FFA name for player " + player.getObjectId() + ": " + e.getMessage());
        }
    }

    /**
     * Remove FFA costume from player (restore original appearance)
     */
    private void removeFFACostume(Player player) {
        try {
            // Get stored original equipment
            List<Item> originalItems = originalEquipment.remove(player.getObjectId());
            if (originalItems == null) {
                originalItems = player.getEquipment().getEquippedForAppearance();
            }

            // Restore appearance exactly like Preview command reset
            PacketSendUtility.sendPacket(player, new SM_CUSTOM_SETTINGS(player));
            PacketSendUtility.sendPacket(player, new SM_UPDATE_PLAYER_APPEARANCE(player.getObjectId(), originalItems));

            // IMPORTANT: Also broadcast the appearance restoration to all nearby players so they can see the change
            PacketSendUtility.broadcastPacket(player, new SM_UPDATE_PLAYER_APPEARANCE(player.getObjectId(), originalItems), false);

        } catch (Exception e) {
            System.err.println("Failed to remove FFA costume from player " + player.getName() + ": " + e.getMessage());
        }
    }

    /**
     * Apply FFA display name to player
     * This approach changes the display name but handles database saves carefully
     */
    private void applyFFAName(Player player) {
        try {
            // Store original name for restoration and tracking
            String originalName = player.getName();
            originalNames.put(player.getObjectId(), originalName);

            // Change the display name to FFA-Challenger
            // We'll handle database constraint issues by temporarily restoring the name during saves
            player.getCommonData().setName(FFA_DISPLAY_NAME);

            // System.out.println("Applied FFA name for player: " + originalName + " -> " + FFA_DISPLAY_NAME);

        } catch (Exception e) {
            System.err.println("Failed to apply FFA name to player " + player.getName() + ": " + e.getMessage());
        }
    }

    /**
     * Remove FFA display name from player and restore original name
     */
    private void removeFFAName(Player player) {
        try {
            // Get stored original name
            String originalName = originalNames.remove(player.getObjectId());
            if (originalName == null) {
                return; // No original name stored
            }

            // Restore original name if player currently has FFA name
            if (FFA_DISPLAY_NAME.equals(player.getName())) {
                player.getCommonData().setName(originalName);
                // System.out.println("Restored original name '" + originalName + "' for player " + player.getObjectId());
            }

        } catch (Exception e) {
            System.err.println("Failed to remove FFA name from player " + player.getName() + ": " + e.getMessage());
        }
    }

    /**
     * Get the display name for a player in FFA context
     * Returns "FFA-Challenger" if player is in FFA event, otherwise their real name
     */
    private String getFFADisplayName(Player player) {
        if (originalNames.containsKey(player.getObjectId())) {
            return FFA_DISPLAY_NAME;
        }
        return player.getName();
    }





    /**
     * Get event status information
     */
    public String getEventStatus() {
        if (!eventActive.get()) {
            return "FFA Event is not active.";
        }

        long remainingTime = (eventStartTime + (CustomConfig.FFA_DURATION_MINUTES * 60 * 1000)) - System.currentTimeMillis();
        int remainingMinutes = (int) (remainingTime / 60000);

        return String.format("FFA Event is active on map %d with %d/%d participants. Time remaining: %d minutes.",
            currentEventMapId, participants.size(), CustomConfig.FFA_MAX_PLAYERS, remainingMinutes);
    }

    /**
     * Get leaderboard information
     */
    public List<String> getLeaderboard() {
        List<String> leaderboard = new ArrayList<>();

        if (allEventParticipants.isEmpty()) {
            leaderboard.add("No participants yet.");
            return leaderboard;
        }

        // Sort participants by kills (descending) - only show participants with kills > 0
        List<FFAParticipant> sortedParticipants = allEventParticipants.values().stream()
            .filter(p -> p.getKills() > 0) // Only show participants with kills
            .sorted((p1, p2) -> Integer.compare(p2.getKills(), p1.getKills()))
            .collect(Collectors.toList());

        if (sortedParticipants.isEmpty()) {
            leaderboard.add("No kills recorded yet. Fight to get on the leaderboard!");
            return leaderboard;
        }

        leaderboard.add("=== FFA Leaderboard ===");
        int rank = 1;
        for (FFAParticipant participant : sortedParticipants) {
            String status = participants.containsKey(participant.getPlayerId()) ? "Active" : "Eliminated";
            // Always show real player names in leaderboard for competitive tracking
            leaderboard.add(String.format("%d. %s - %d kills (%s)",
                rank++, participant.getPlayerName(), participant.getKills(), status));

            if (rank > 10) break; // Show top 10
        }

        return leaderboard;
    }

    /**
     * Get leaderboard as StringBuilder for command handlers
     */
    public StringBuilder getLeaderboard(int count) {
        StringBuilder sb = new StringBuilder();

        if (allEventParticipants.isEmpty()) {
            sb.append("No FFA statistics available yet.");
            return sb;
        }

        // Sort participants by kills (descending) - only show participants with kills > 0
        List<FFAParticipant> sortedParticipants = allEventParticipants.values().stream()
            .filter(p -> p.getKills() > 0) // Only show participants with kills
            .sorted((p1, p2) -> Integer.compare(p2.getKills(), p1.getKills()))
            .limit(count)
            .collect(Collectors.toList());

        // System.out.println("DEBUG: Filtered participants with kills > 0: " + sortedParticipants.size());

        if (sortedParticipants.isEmpty()) {
            sb.append("No kills recorded yet. Fight to get on the leaderboard!");
            return sb;
        }

        sb.append("=== FFA Leaderboard (Top ").append(count).append(") ===\n");
        int rank = 1;
        for (FFAParticipant participant : sortedParticipants) {
            String status = participants.containsKey(participant.getPlayerId()) ? "Active" : "Eliminated";
            // Always show real player names in leaderboard for competitive tracking
            sb.append(String.format("%d. %s - %d kills (%s)\n",
                rank++, participant.getPlayerName(), participant.getKills(), status));
        }

        return sb;
    }

    /**
     * Get current participants as StringBuilder for command handlers
     */
    public StringBuilder getCurrentParticipants() {
        StringBuilder sb = new StringBuilder();

        if (participants.isEmpty()) {
            sb.append("No active participants in the current FFA event.");
            return sb;
        }

        sb.append("=== Current FFA Participants ===\n");
        sb.append("Active: ").append(participants.size()).append("/").append(CustomConfig.FFA_MAX_PLAYERS).append("\n\n");

        // Sort by kills
        List<FFAParticipant> sortedParticipants = participants.values().stream()
            .sorted((p1, p2) -> Integer.compare(p2.getKills(), p1.getKills()))
            .collect(Collectors.toList());

        int rank = 1;
        for (FFAParticipant participant : sortedParticipants) {
            Player player = World.getInstance().getPlayer(participant.getPlayerId());
            String status = (player != null && player.isOnline()) ? "Online" : "Offline";
            sb.append(String.format("%d. %s - %d kills (%s)\n",
                rank++, participant.getPlayerName(), participant.getKills(), status));
        }

        return sb;
    }

    /**
     * Distribute rewards to participants
     */
    private void distributeRewards() {
        if (allEventParticipants.isEmpty()) {
            return;
        }

        // System.out.println("Distributing FFA rewards to " + allEventParticipants.size() + " participants");

        // Sort participants by kills (descending)
        List<FFAParticipant> sortedParticipants = allEventParticipants.values().stream()
            .sorted((p1, p2) -> Integer.compare(p2.getKills(), p1.getKills()))
            .collect(Collectors.toList());

        // Distribute rewards
        for (int i = 0; i < sortedParticipants.size(); i++) {
            FFAParticipant participant = sortedParticipants.get(i);
            String rewardConfig;
            String rewardTitle;

            if (i == 0) {
                rewardConfig = CustomConfig.FFA_REWARDS_FIRST;
                rewardTitle = "1st Place";
            } else if (i == 1) {
                rewardConfig = CustomConfig.FFA_REWARDS_SECOND;
                rewardTitle = "2nd Place";
            } else if (i == 2) {
                rewardConfig = CustomConfig.FFA_REWARDS_THIRD;
                rewardTitle = "3rd Place";
            } else {
                rewardConfig = CustomConfig.FFA_REWARDS_PARTICIPATION;
                rewardTitle = "Participation";
            }

            sendReward(participant, rewardConfig, rewardTitle, i + 1, participant.getKills());
        }
    }

    /**
     * Send reward to participant
     */
    private void sendReward(FFAParticipant participant, String rewardConfig, String rewardTitle, int rank, int kills) {
        try {
            String[] rewards = rewardConfig.split(":");
            if (rewards.length < 2) {
                // System.out.println("Invalid reward configuration: " + rewardConfig);
                return;
            }

            int itemId = Integer.parseInt(rewards[0]);
            long itemCount = Long.parseLong(rewards[1]);
            long kinah = rewards.length > 2 ? Long.parseLong(rewards[2]) : 0;

            String subject = String.format("FFA Event Reward - %s", rewardTitle);
            String message = String.format("Congratulations! You finished %s in the FFA Event with %d kills.\n\nReward: %d x %s",
                getOrdinalNumber(rank), kills, itemCount, getItemName(itemId));

            if (kinah > 0) {
                message += String.format("\nKinah: %,d", kinah);
            }

            // Send mail with rewards via EXPRESS mail
            SystemMailService.sendMail("FFA-System", participant.getPlayerName(),
                subject, message, itemId, itemCount, kinah, LetterType.EXPRESS);

            // System.out.println("Sent FFA reward to " + participant.getPlayerName() + ": " + rewardTitle + " (Rank: " + rank + ", Kills: " + kills + ")");

        } catch (Exception e) {
            // System.out.println("Error sending FFA reward to " + participant.getPlayerName() + ": " + e.getMessage());
        }
    }

    /**
     * Get ordinal number (1st, 2nd, 3rd, etc.)
     */
    private String getOrdinalNumber(int number) {
        if (number >= 11 && number <= 13) {
            return number + "th";
        }
        switch (number % 10) {
            case 1: return number + "st";
            case 2: return number + "nd";
            case 3: return number + "rd";
            default: return number + "th";
        }
    }

    /**
     * Get item name by ID
     */
    private String getItemName(int itemId) {
        try {
            ItemTemplate template = DataManager.ITEM_DATA.getItemTemplate(itemId);
            return template != null ? template.getName() : "Unknown Item";
        } catch (Exception e) {
            return "Unknown Item";
        }
    }

    // Getter methods for external access
    public boolean isEventActive() {
        return eventActive.get();
    }

    public int getParticipantCount() {
        return participants.size();
    }

    public int getMaxParticipants() {
        return CustomConfig.FFA_MAX_PLAYERS;
    }

    public boolean isParticipant(Player player) {
        return participants.containsKey(player.getObjectId());
    }

    public WorldMapInstance getEventInstance() {
        return eventInstance;
    }

    public int getCurrentEventMapId() {
        return currentEventMapId;
    }

    /**
     * Update enemy state for all FFA players to ensure same-race PvP works
     */
    private void updateAllPlayersEnemyState() {
        try {
            for (FFAParticipant participant : participants.values()) {
                Player player = World.getInstance().getPlayer(participant.getPlayerId());
                if (player != null && player.isOnline()) {
                    // Ensure ENEMY_OF_ALL_PLAYERS state is set
                    player.setCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);
                    player.getController().onChangedPlayerAttributes();
                }
            }
            // System.out.println("Updated enemy state for all " + participants.size() + " FFA participants");
        } catch (Exception e) {
            // System.out.println("Error updating enemy states for FFA players: " + e.getMessage());
        }
    }

    /**
     * Monitor players for stuck death states and automatically fix them
     */
    public void monitorDeathStates() {
        if (!eventActive.get() || participants.isEmpty()) {
            return;
        }

        long currentTime = System.currentTimeMillis();
        final long DEATH_TIMEOUT_MS = 1000; // 1 second - faster resurrection for more dynamic gameplay

        try {
            // Check all active FFA participants for death state issues
            for (FFAParticipant participant : participants.values()) {
                Player player = World.getInstance().getPlayer(participant.getPlayerId());
                if (player == null || !player.isOnline()) {
                    // Clean up death tracking for offline players
                    playerDeathTimes.remove(participant.getPlayerId());
                    continue;
                }

                // Check if player is dead
                if (player.isDead()) {
                    Long deathTime = playerDeathTimes.get(participant.getPlayerId());

                    if (deathTime == null) {
                        // First time we detect this player is dead, record the time
                        playerDeathTimes.put(participant.getPlayerId(), currentTime);
                        // System.out.println("FFA: Detected player " + player.getName() + " is dead, starting death timer");
                    } else {
                        // Check if player has been dead too long
                        long deadDuration = currentTime - deathTime;
                        if (deadDuration >= DEATH_TIMEOUT_MS) {
                            // System.out.println("FFA: Player " + player.getName() + " has been stuck in death state for " + deadDuration + "ms, auto-fixing");

                            // Auto-fix the stuck death state for FFA
                            autoFixStuckDeathStateInFFA(player, participant);

                            // Remove from death tracking
                            playerDeathTimes.remove(participant.getPlayerId());
                        }
                    }
                } else {
                    // Player is alive, remove from death tracking if present
                    if (playerDeathTimes.remove(participant.getPlayerId()) != null) {
                        // System.out.println("FFA: Player " + player.getName() + " is no longer dead, removed from death tracking");
                    }
                }
            }
        } catch (Exception e) {
            // System.out.println("Error monitoring death states in FFA: " + e.getMessage());
        }
    }
	/**
 * Manual resurrection without popup
 */
private void manualResurrect(Player player) {
    try {
        // Use standard resurrection service to prevent resurrection popup
        PlayerReviveService.revive(player, 100, 100, false, 0);
        
        // Additional state cleanup
        player.unsetResPosState();
        player.setPlayerResActivate(false);
        player.getEffectController().removeEffect(8291); // Soul Sickness
        player.getGameStats().updateStatsAndSpeedVisually();
        
        // Force client update
        PacketSendUtility.broadcastPacket(player, new SM_EMOTION(player, EmotionType.RESURRECT, 0, 0), true);
    } catch (Exception e) {
        System.err.println("Error during manual resurrection: " + e.getMessage());
    }
}
    /**
     * Automatically fix a player stuck in death state in FFA
     */
    // Update autoFixStuckDeathStateInFFA
private void autoFixStuckDeathStateInFFA(Player player, FFAParticipant participant) {
    try {
        manualResurrect(player); // Use manual resurrection
        
        // Ensure FFA enemy state is maintained
        player.setCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);
        player.getController().onChangedPlayerAttributes();

        // Teleport to random spawn location in the FFA instance
        if (eventInstance != null && player.getInstanceId() == eventInstance.getInstanceId()) {
            float[] coords = getRandomSpawnCoordinates(currentEventMapId);
            TeleportService.teleportTo(player, eventInstance, coords[0], coords[1], coords[2], (byte) coords[3]);
        }

        // Send message to player
        PacketSendUtility.sendMessage(player,
            "You were automatically resurrected with full HP/MP in the FFA arena.",
            ChatType.BRIGHT_YELLOW_CENTER);

    } catch (Exception e) {
        System.err.println("Error auto-fixing stuck death state: " + e.getMessage());
    }
}


// Update manualResurrectPlayer command
public void manualResurrectPlayer(Player player) {
    if (!isEventActive() || participants.get(player.getObjectId()) == null) {
        PacketSendUtility.sendMessage(player, "You are not in an active FFA event.", ChatType.BRIGHT_YELLOW_CENTER);
        return;
    }

    try {
        manualResurrect(player); // Use manual resurrection

        // Teleport to random spawn location
        if (eventInstance != null && player.getInstanceId() == eventInstance.getInstanceId()) {
            float[] coords = getRandomSpawnCoordinates(currentEventMapId);
            TeleportService.teleportTo(player, eventInstance, coords[0], coords[1], coords[2], (byte) coords[3]);
        }

        // Send success message
        PacketSendUtility.sendMessage(player,
            "You have been manually resurrected with full HP/MP in the FFA arena.",
            ChatType.BRIGHT_YELLOW_CENTER);

    } catch (Exception e) {
        System.err.println("Error during manual resurrection: " + e.getMessage());
        PacketSendUtility.sendMessage(player, 
            "Error during resurrection. Please contact an administrator.", 
            ChatType.BRIGHT_YELLOW_CENTER);
    }
}
    /**
     * Inner class for participant data
     */
    public static class FFAParticipant {
        private final int playerId;
        private final String playerName;
        private int kills = 0;
        private long joinTime;

        public FFAParticipant(int playerId, String playerName) {
            this.playerId = playerId;
            this.playerName = playerName;
            this.joinTime = System.currentTimeMillis();
        }

        // Getters and setters
        public int getPlayerId() { return playerId; }
        public String getPlayerName() { return playerName; }
        public int getKills() { return kills; }
        public void incrementKills() {
            this.kills++;
        }
        public long getJoinTime() { return joinTime; }

        // Method for rejoining (preserves kills but updates join time)
        public void rejoin() {
            this.joinTime = System.currentTimeMillis();
        }
    }
}
