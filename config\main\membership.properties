#
# ----------------------------
# Membership config's:
# ----------------------------
#
# Options in this file are enabled for players with the given membership level or above.
# 0 means regular players, since it's the default membership of new players
# 1 means players with membership level 1 and so on

# Comma separated membership names for notification on login, starting with the name for membership 1
# Default: Premium
gameserver.membership.types = Premium

# Allow gathering while riding a mount
# Default: 10
gameserver.membership.gathering.allow_on_mount = 10

# Disable instances title requirement
# Default: 10
gameserver.instances.title.requirement = 10

# Disable instances race requirement
# Default: 10
gameserver.instances.race.requirement = 10

# Disable instances level requirement
# Default: 10
gameserver.instances.level.requirement = 10

# Disable instances group requirement
# Default: 10
gameserver.instances.group.requirement = 10

# Disable instances quest requirement
# Default: 10
gameserver.instances.quest.requirement = 10

# Decrease instances cooldowns
# Default: 10
gameserver.instances.cooldown = 10

# All emotions available
# Default: 10
gameserver.emotions.all = 10

# Not require quest for normal/advanced stigma slots
# Default: 10
gameserver.quest.stigma.slot = 10

# Disable soulsickness (debuf after death)
# Default: 10
gameserver.soulsickness.disable = 10

# Enable stigma auto learn mode
# Default: 10
gameserver.autolearn.stigma = 10

# Disable quest limit
# Default: 10
gameserver.quest.limit.disable = 10

# Enable additional count of characters per account
# Default: 10
gameserver.character.additional.enable = 10

# ----------------------------
# Membership Options:
# ----------------------------
# Count of characters for this membership (max 8)
# Default: 8
gameserver.character.additional.count = 8