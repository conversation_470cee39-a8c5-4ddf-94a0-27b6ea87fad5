#
# ----------------------------
# Database Cleaning config's:
# ----------------------------

# Enable Database Cleaning
# Default: false
gameserver.cleaning.enable = false

# Minimum account inactivity in days, after which chars get deleted
# Cleaning will only be executed with a value greater than 30
# Default: 365
gameserver.cleaning.min_account_inactivity = 365

# Maximum level of characters that will be deleted on each account
# Default: 25
gameserver.cleaning.max_level = 25