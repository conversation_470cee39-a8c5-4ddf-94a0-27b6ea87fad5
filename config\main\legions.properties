#
# ----------------------------
# Legions config's:
# ----------------------------
# NOTE: Some config is only server side supported!

# Character patterns
# To enable UTF support - set this parameter to [a-zA-Z\u0410-\u042f\u0430-\u044f]{2,32}
gameserver.legion.pattern = [a-zA-Z ]{2,32}
gameserver.legion.selfintropattern = .{1,32}
gameserver.legion.nicknamepattern = .{1,10}

# Disband settings
gameserver.legion.disbandtime = 86400

# Legion creation settings
gameserver.legion.creationrequiredkinah = 10000

# Legion emblem settings
gameserver.legion.emblemrequiredkinah = 800000

# Legion level up settings
gameserver.legion.level2requiredkinah = 100000
gameserver.legion.level3requiredkinah = 1000000
gameserver.legion.level4requiredkinah = 5000000
gameserver.legion.level5requiredkinah = 25000000
gameserver.legion.level6requiredkinah = 50000000
gameserver.legion.level7requiredkinah = 75000000
gameserver.legion.level8requiredkinah = 100000000
gameserver.legion.level2requiredmembers = 6
gameserver.legion.level3requiredmembers = 6
gameserver.legion.level4requiredmembers = 6
gameserver.legion.level5requiredmembers = 6
gameserver.legion.level6requiredmembers = 6
gameserver.legion.level7requiredmembers = 6
gameserver.legion.level8requiredmembers = 6
gameserver.legion.level2requiredcontribution = 0
gameserver.legion.level3requiredcontribution = 20000
gameserver.legion.level4requiredcontribution = 100000
gameserver.legion.level5requiredcontribution = 500000
gameserver.legion.level6requiredcontribution = 2500000
gameserver.legion.level7requiredcontribution = 12500000
gameserver.legion.level8requiredcontribution = 62500000

# Legion member settings
gameserver.legion.level1maxmembers = 30
gameserver.legion.level2maxmembers = 60
gameserver.legion.level3maxmembers = 90
gameserver.legion.level4maxmembers = 120
gameserver.legion.level5maxmembers = 150
gameserver.legion.level6maxmembers = 180
gameserver.legion.level7maxmembers = 210
gameserver.legion.level8maxmembers = 240

# Legion functions
gameserver.legion.warehouse = true
gameserver.legion.inviteotherfaction = false

# Enable Guild Task requirement for level up
# Tasks are needed for leveling legion over level 5
# If set to false this condition is disabled
# Default: true
gameserver.legion.task.requirement.enable = true

# Enable/Disable legion dominion key requirement
# Default: true
gameserver.legion.require_key_for_stonespear_reach = true

# Min points to be reached in stonespear reach instance to account for a territory election
# Default: 0
gameserver.legion.stonespear_reach_min_points = 0