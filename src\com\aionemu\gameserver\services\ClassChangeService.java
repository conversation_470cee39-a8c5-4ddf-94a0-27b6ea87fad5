package com.aionemu.gameserver.services;
import com.aionemu.gameserver.services.StigmaService;
import static com.aionemu.gameserver.model.DialogAction.*;
import com.aionemu.gameserver.configs.main.GSConfig;
import com.aionemu.gameserver.configs.main.MembershipConfig;
import com.aionemu.gameserver.model.PlayerClass;
import com.aionemu.gameserver.model.Race;
import com.aionemu.gameserver.services.item.ItemService;
import com.aionemu.gameserver.model.animations.ActionAnimation;
import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.network.aion.serverpackets.SM_ACTION_ANIMATION;
import com.aionemu.gameserver.network.aion.serverpackets.SM_DIALOG_WINDOW;
import com.aionemu.gameserver.network.aion.serverpackets.SM_PLAYER_INFO;
import com.aionemu.gameserver.network.aion.serverpackets.SM_QUEST_ACTION;
import com.aionemu.gameserver.network.aion.serverpackets.SM_QUEST_ACTION.ActionType;
import com.aionemu.gameserver.questEngine.model.QuestState;
import com.aionemu.gameserver.questEngine.model.QuestStatus;
import com.aionemu.gameserver.utils.PacketSendUtility;
import com.aionemu.gameserver.utils.ThreadPoolManager;
import com.aionemu.gameserver.model.gameobjects.player.PlayerCommonData;
import com.aionemu.gameserver.configs.main.CustomConfig;
import com.aionemu.gameserver.model.items.storage.StorageType;
import com.aionemu.gameserver.network.aion.serverpackets.SM_CUBE_UPDATE;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
/**
 * <AUTHOR> sweetkr, Neon
 */
public class ClassChangeService {

    public static void showClassChangeDialog(Player player) {
		PlayerClass playerClass = player.getPlayerClass();
		Race playerRace = player.getRace();
		if (player.getLevel() >= 9 && playerClass.isStartingClass())
			PacketSendUtility.sendPacket(player,
				new SM_DIALOG_WINDOW(0, getClassSelectionDialogPageId(playerRace, playerClass), playerRace == Race.ELYOS ? 1006 : 2008));
	}
	private static final Map<PlayerClass, Map<Race, List<ItemReward>>> CLASS_REWARDS = new HashMap<>();

    static {
        // GLADIATOR
        CLASS_REWARDS.put(PlayerClass.GLADIATOR, new HashMap<Race, List<ItemReward>>(){{
            put(Race.ELYOS, Arrays.asList(
                new ItemReward(110600991, 1),
                new ItemReward(113600960, 1),
                new ItemReward(111600977, 1),
                new ItemReward(112600950, 1),
                new ItemReward(114600957, 1),
                new ItemReward(125002336, 1),
                new ItemReward(120001059, 1),
                new ItemReward(122001196, 1),
                new ItemReward(121000969, 1),
                new ItemReward(123001058, 1),
                new ItemReward(120001059, 1),
                new ItemReward(122001196, 1),
                new ItemReward(100900710, 1),
                new ItemReward(101300680, 1),
                new ItemReward(101300690, 1),
                new ItemReward(100900720, 1),
                new ItemReward(187000024, 1),
                new ItemReward(167000954, 100),
                new ItemReward(167000962, 100),
                new ItemReward(167000957, 100),
                new ItemReward(190020087, 1),
                new ItemReward(166000190, 100),
                new ItemReward(188053787, 10),
                new ItemReward(188053788, 5),
                new ItemReward(188053789, 3)
            ));
            put(Race.ASMODIANS, Arrays.asList(
                new ItemReward(110600995, 1),
                new ItemReward(113600964, 1),
                new ItemReward(112600954, 1),
                new ItemReward(114600961, 1),
                new ItemReward(111600981, 1),
                new ItemReward(125002352, 1),
                new ItemReward(120001067, 1),
                new ItemReward(122001204, 1),
                new ItemReward(121000977, 1),
                new ItemReward(123001066, 1),
                new ItemReward(120001067, 1),
                new ItemReward(122001204, 1),
                new ItemReward(100900710, 1),
                new ItemReward(101300680, 1),
                new ItemReward(101300695, 1),
                new ItemReward(100900725, 1),
                new ItemReward(187000024, 1),
                new ItemReward(167000954, 100),
                new ItemReward(167000962, 100),
                new ItemReward(167000957, 100),
                new ItemReward(190020087, 1),
                new ItemReward(166000190, 100),
                new ItemReward(188053787, 10),
                new ItemReward(188053788, 5),
                new ItemReward(188053789, 3)
            ));
        }});

        // TEMPLAR
        CLASS_REWARDS.put(PlayerClass.TEMPLAR, new HashMap<Race, List<ItemReward>>(){{
            put(Race.ELYOS, Arrays.asList(
                new ItemReward(110600991, 1),
                new ItemReward(113600960, 1),
                new ItemReward(111600977, 1),
                new ItemReward(112600950, 1),
                new ItemReward(114600957, 1),
                new ItemReward(125002336, 1),
                new ItemReward(120001059, 1),
                new ItemReward(122001196, 1),
                new ItemReward(121000969, 1),
                new ItemReward(123001058, 1),
                new ItemReward(120001059, 1),
                new ItemReward(122001196, 1),
                new ItemReward(100000929, 1),
                new ItemReward(100100699, 1),
                new ItemReward(100900706, 1),
                new ItemReward(115000993, 1),
                new ItemReward(100000943, 1),
                new ItemReward(100100715, 1),
                new ItemReward(100900720, 1),
                new ItemReward(115000966, 1),
                new ItemReward(187000024, 1),
                new ItemReward(167000954, 100),
                new ItemReward(167000961, 100),
                new ItemReward(167000955, 100),
                new ItemReward(167000960, 100),
                new ItemReward(190020087, 1),
                new ItemReward(166000190, 100),
                new ItemReward(188053787, 10),
                new ItemReward(188053788, 5),
                new ItemReward(188053789, 3)
            ));
            put(Race.ASMODIANS, Arrays.asList(
                new ItemReward(110600995, 1),
                new ItemReward(113600964, 1),
                new ItemReward(112600954, 1),
                new ItemReward(114600961, 1),
                new ItemReward(111600981, 1),
                new ItemReward(125002352, 1),
                new ItemReward(120001067, 1),
                new ItemReward(122001204, 1),
                new ItemReward(121000977, 1),
                new ItemReward(123001066, 1),
                new ItemReward(120001067, 1),
                new ItemReward(122001204, 1),
                new ItemReward(100900710, 1),
                new ItemReward(100100703, 1),
                new ItemReward(115000997, 1),
                new ItemReward(100000933, 1),
                new ItemReward(100000948, 1),
                new ItemReward(100100720, 1),
                new ItemReward(100900725, 1),
                new ItemReward(115000966, 1),
                new ItemReward(187000024, 1),
                new ItemReward(167000954, 100),
                new ItemReward(167000961, 100),
                new ItemReward(167000955, 100),
                new ItemReward(167000960, 100),
                new ItemReward(190020087, 1),
                new ItemReward(166000190, 100),
                new ItemReward(188053787, 10),
                new ItemReward(188053788, 5),
                new ItemReward(188053789, 3)
            ));
        }});

        // ASSASSIN
        CLASS_REWARDS.put(PlayerClass.ASSASSIN, new HashMap<Race, List<ItemReward>>(){{
            put(Race.ELYOS, Arrays.asList(
                new ItemReward(110301043, 1),
                new ItemReward(113301019, 1),
                new ItemReward(111300999, 1),
                new ItemReward(112300948, 1),
                new ItemReward(114301055, 1),
                new ItemReward(125002334, 1),
                new ItemReward(120001059, 1),
                new ItemReward(122001196, 1),
                new ItemReward(121000969, 1),
                new ItemReward(123001058, 1),
                new ItemReward(120001059, 1),
                new ItemReward(122001196, 1),
                new ItemReward(100000929, 1),
                new ItemReward(100200829, 1),
                new ItemReward(100000943, 1),
                new ItemReward(100200843, 1),
                new ItemReward(187000024, 1),
                new ItemReward(167000954, 100),
                new ItemReward(167000962, 100),
                new ItemReward(190020087, 1),
                new ItemReward(166000190, 100),
                new ItemReward(188053787, 10),
                new ItemReward(188053788, 5),
                new ItemReward(188053789, 3)
            ));
            put(Race.ASMODIANS, Arrays.asList(
                new ItemReward(110301047, 1),
                new ItemReward(113301023, 1),
                new ItemReward(111301003, 1),
                new ItemReward(112300952, 1),
                new ItemReward(114301059, 1),
                new ItemReward(125002350, 1),
                new ItemReward(120001067, 1),
                new ItemReward(122001204, 1),
                new ItemReward(121000977, 1),
                new ItemReward(123001066, 1),
                new ItemReward(120001067, 1),
                new ItemReward(122001204, 1),
                new ItemReward(100000933, 1),
                new ItemReward(100200833, 1),
                new ItemReward(100000948, 1),
                new ItemReward(100200848, 1),
                new ItemReward(187000024, 1),
                new ItemReward(167000954, 100),
                new ItemReward(167000962, 100),
                new ItemReward(190020087, 1),
                new ItemReward(166000190, 100),
                new ItemReward(188053787, 10),
                new ItemReward(188053788, 5),
                new ItemReward(188053789, 3)
            ));
        }});

        // RANGER
        CLASS_REWARDS.put(PlayerClass.RANGER, new HashMap<Race, List<ItemReward>>(){{
            put(Race.ELYOS, Arrays.asList(
                new ItemReward(110301043, 1),
                new ItemReward(113301019, 1),
                new ItemReward(111300999, 1),
                new ItemReward(112300948, 1),
                new ItemReward(114301055, 1),
                new ItemReward(125002334, 1),
                new ItemReward(120001059, 1),
                new ItemReward(122001196, 1),
                new ItemReward(121000969, 1),
                new ItemReward(123001058, 1),
                new ItemReward(120001059, 1),
                new ItemReward(122001196, 1),
                new ItemReward(101700744, 1),
                new ItemReward(101700758, 1),
                new ItemReward(187000024, 1),
                new ItemReward(167000954, 100),
                new ItemReward(167000962, 100),
                new ItemReward(190020087, 1),
                new ItemReward(166000190, 100),
                new ItemReward(188053787, 10),
                new ItemReward(188053788, 5),
                new ItemReward(188053789, 3)
            ));
            put(Race.ASMODIANS, Arrays.asList(
                new ItemReward(110301047, 1),
                new ItemReward(113301023, 1),
                new ItemReward(111301003, 1),
                new ItemReward(112300952, 1),
                new ItemReward(114301059, 1),
                new ItemReward(125002350, 1),
                new ItemReward(120001067, 1),
                new ItemReward(122001204, 1),
                new ItemReward(121000977, 1),
                new ItemReward(123001066, 1),
                new ItemReward(120001067, 1),
                new ItemReward(122001204, 1),
                new ItemReward(101700748, 1),
                new ItemReward(101700763, 1),
                new ItemReward(187000024, 1),
                new ItemReward(167000954, 100),
                new ItemReward(167000962, 100),
                new ItemReward(190020087, 1),
                new ItemReward(166000190, 100),
                new ItemReward(188053787, 10),
                new ItemReward(188053788, 5),
                new ItemReward(188053789, 3)
            ));
        }});

        // SORCERER
        CLASS_REWARDS.put(PlayerClass.SORCERER, new HashMap<Race, List<ItemReward>>(){{
            put(Race.ELYOS, Arrays.asList(
                new ItemReward(110101094, 1),
                new ItemReward(113101006, 1),
                new ItemReward(111100994, 1),
                new ItemReward(112100950, 1),
                new ItemReward(114101035, 1),
                new ItemReward(125002333, 1),
                new ItemReward(120001060, 1),
                new ItemReward(122001197, 1),
                new ItemReward(121000970, 1),
                new ItemReward(123001059, 1),
                new ItemReward(120001060, 1),
                new ItemReward(122001197, 1),
                new ItemReward(100500720, 1),
                new ItemReward(100600777, 1),
                new ItemReward(100500734, 1),
                new ItemReward(100600791, 1),
                new ItemReward(187000024, 1),
                new ItemReward(167000959, 100),
                new ItemReward(167000964, 100),
                new ItemReward(190020087, 1),
                new ItemReward(166000190, 100),
                new ItemReward(188053787, 10),
                new ItemReward(188053788, 5),
                new ItemReward(188053789, 3)
            ));
            put(Race.ASMODIANS, Arrays.asList(
                new ItemReward(110101098, 1),
                new ItemReward(113101010, 1),
                new ItemReward(111100998, 1),
                new ItemReward(112100954, 1),
                new ItemReward(114101039, 1),
                new ItemReward(125002349, 1),
                new ItemReward(120001068, 1),
                new ItemReward(122001205, 1),
                new ItemReward(121000978, 1),
                new ItemReward(123001067, 1),
                new ItemReward(120001068, 1),
                new ItemReward(122001205, 1),
                new ItemReward(100600781, 1),
                new ItemReward(100500724, 1),
                new ItemReward(100500739, 1),
                new ItemReward(100600796, 1),
                new ItemReward(187000024, 1),
                new ItemReward(167000959, 100),
                new ItemReward(167000964, 100),
                new ItemReward(190020087, 1),
                new ItemReward(166000190, 100),
                new ItemReward(188053787, 10),
                new ItemReward(188053788, 5),
                new ItemReward(188053789, 3)
            ));
        }});

        // SPIRIT_MASTER
        CLASS_REWARDS.put(PlayerClass.SPIRIT_MASTER, new HashMap<Race, List<ItemReward>>(){{
            put(Race.ELYOS, Arrays.asList(
                new ItemReward(110101094, 1),
                new ItemReward(113101006, 1),
                new ItemReward(111100994, 1),
                new ItemReward(112100950, 1),
                new ItemReward(114101035, 1),
                new ItemReward(125002333, 1),
                new ItemReward(120001060, 1),
                new ItemReward(122001197, 1),
                new ItemReward(121000970, 1),
                new ItemReward(123001059, 1),
                new ItemReward(120001060, 1),
                new ItemReward(122001197, 1),
                new ItemReward(100500720, 1),
                new ItemReward(100600777, 1),
                new ItemReward(100500734, 1),
                new ItemReward(100600791, 1),
                new ItemReward(187000024, 1),
                new ItemReward(167000959, 100),
                new ItemReward(167000964, 100),
                new ItemReward(190020087, 1),
                new ItemReward(166000190, 100),
                new ItemReward(188053787, 10),
                new ItemReward(188053788, 5),
                new ItemReward(188053789, 3)
            ));
            put(Race.ASMODIANS, Arrays.asList(
                new ItemReward(110101098, 1),
                new ItemReward(113101010, 1),
                new ItemReward(111100998, 1),
                new ItemReward(112100954, 1),
                new ItemReward(114101039, 1),
                new ItemReward(125002349, 1),
                new ItemReward(120001068, 1),
                new ItemReward(122001205, 1),
                new ItemReward(121000978, 1),
                new ItemReward(123001067, 1),
                new ItemReward(120001068, 1),
                new ItemReward(122001205, 1),
                new ItemReward(100600781, 1),
                new ItemReward(100500724, 1),
                new ItemReward(100500739, 1),
                new ItemReward(100600796, 1),
                new ItemReward(187000024, 1),
                new ItemReward(167000959, 100),
                new ItemReward(167000964, 100),
                new ItemReward(190020087, 1),
                new ItemReward(166000190, 100),
                new ItemReward(188053787, 10),
                new ItemReward(188053788, 5),
                new ItemReward(188053789, 3)
            ));
        }});

        // CLERIC
        CLASS_REWARDS.put(PlayerClass.CLERIC, new HashMap<Race, List<ItemReward>>(){{
            put(Race.ELYOS, Arrays.asList(
                new ItemReward(110501011, 1),
                new ItemReward(113500989, 1),
                new ItemReward(111500986, 1),
                new ItemReward(112500935, 1),
                new ItemReward(114500998, 1),
                new ItemReward(120001060, 1),
                new ItemReward(122001197, 1),
                new ItemReward(121000970, 1),
                new ItemReward(123001059, 1),
                new ItemReward(120001060, 1),
                new ItemReward(122001197, 1),
                new ItemReward(125002335, 1),
                new ItemReward(100100699, 1),
                new ItemReward(101500721, 1),
                new ItemReward(115000993, 1),
                new ItemReward(101500735, 1),
                new ItemReward(100100715, 1),
                new ItemReward(115000967, 1),
                new ItemReward(187000024, 1),
                new ItemReward(167000955, 100),
                new ItemReward(167000959, 100),
                new ItemReward(167000563, 100),
                new ItemReward(190020087, 1),
                new ItemReward(166000190, 100),
                new ItemReward(188053787, 10),
                new ItemReward(188053788, 5),
                new ItemReward(188053789, 3)
            ));
            put(Race.ASMODIANS, Arrays.asList(
                new ItemReward(110501015, 1),
                new ItemReward(113500993, 1),
                new ItemReward(111500990, 1),
                new ItemReward(112500939, 1),
                new ItemReward(114501002, 1),
                new ItemReward(125002351, 1),
                new ItemReward(120001068, 1),
                new ItemReward(122001205, 1),
                new ItemReward(121000978, 1),
                new ItemReward(123001067, 1),
                new ItemReward(120001068, 1),
                new ItemReward(122001205, 1),
                new ItemReward(100100703, 1),
                new ItemReward(101500725, 1),
                new ItemReward(115000997, 1),
                new ItemReward(101500740, 1),
                new ItemReward(100100720, 1),
                new ItemReward(115000967, 1),
                new ItemReward(187000024, 1),
                new ItemReward(167000955, 100),
                new ItemReward(167000959, 100),
                new ItemReward(167000563, 100),
                new ItemReward(190020087, 1),
                new ItemReward(166000190, 100),
                new ItemReward(188053787, 10),
                new ItemReward(188053788, 5),
                new ItemReward(188053789, 3)
            ));
        }});

        // CHANTER
        CLASS_REWARDS.put(PlayerClass.CHANTER, new HashMap<Race, List<ItemReward>>(){{
            put(Race.ELYOS, Arrays.asList(
                new ItemReward(110501011, 1),
                new ItemReward(113500989, 1),
                new ItemReward(111500986, 1),
                new ItemReward(112500935, 1),
                new ItemReward(114500998, 1),
                new ItemReward(120001059, 1),
                new ItemReward(122001196, 1),
                new ItemReward(121000969, 1),
                new ItemReward(123001058, 1),
                new ItemReward(120001059, 1),
                new ItemReward(122001196, 1),
                new ItemReward(100100699, 1),
                new ItemReward(101500721, 1),
                new ItemReward(115000993, 1),
                new ItemReward(125002335, 1),
                new ItemReward(101500735, 1),
                new ItemReward(100100715, 1),
                new ItemReward(115000966, 1),
                new ItemReward(187000024, 1),
                new ItemReward(167000954, 100),
                new ItemReward(167000962, 100),
                new ItemReward(190020087, 1),
                new ItemReward(166000190, 100),
                new ItemReward(188053787, 10),
                new ItemReward(188053788, 5),
                new ItemReward(188053789, 3)
            ));
            put(Race.ASMODIANS, Arrays.asList(
                new ItemReward(110501015, 1),
                new ItemReward(113500993, 1),
                new ItemReward(111500990, 1),
                new ItemReward(112500939, 1),
                new ItemReward(114501002, 1),
                new ItemReward(125002351, 1),
                new ItemReward(120001067, 1),
                new ItemReward(122001204, 1),
                new ItemReward(121000977, 1),
                new ItemReward(123001066, 1),
                new ItemReward(120001067, 1),
                new ItemReward(122001204, 1),
                new ItemReward(100100703, 1),
                new ItemReward(101500725, 1),
                new ItemReward(115000997, 1),
                new ItemReward(101500740, 1),
                new ItemReward(100100720, 1),
                new ItemReward(115000966, 1),
                new ItemReward(187000024, 1),
                new ItemReward(167000954, 100),
                new ItemReward(167000962, 100),
                new ItemReward(190020087, 1),
                new ItemReward(166000190, 100),
                new ItemReward(188053787, 10),
                new ItemReward(188053788, 5),
                new ItemReward(188053789, 3)
            ));
        }});

        // GUNNER
        CLASS_REWARDS.put(PlayerClass.GUNNER, new HashMap<Race, List<ItemReward>>(){{
            put(Race.ELYOS, Arrays.asList(
                new ItemReward(110301573, 1),
                new ItemReward(113301539, 1),
                new ItemReward(111301511, 1),
                new ItemReward(112301452, 1),
                new ItemReward(114301577, 1),
                new ItemReward(125003564, 1),
                new ItemReward(120001060, 1),
                new ItemReward(122001197, 1),
                new ItemReward(121000970, 1),
                new ItemReward(123001059, 1),
                new ItemReward(120001060, 1),
                new ItemReward(122001197, 1),
                new ItemReward(101800643, 1),
                new ItemReward(101900642, 1),
                new ItemReward(101800654, 1),
                new ItemReward(101900654, 1),
                new ItemReward(187000024, 1),
                new ItemReward(167000959, 100),
                new ItemReward(167000964, 100),
                new ItemReward(190020087, 1),
                new ItemReward(166000190, 100),
                new ItemReward(188053787, 10),
                new ItemReward(188053788, 5),
                new ItemReward(188053789, 3)
            ));
            put(Race.ASMODIANS, Arrays.asList(
                new ItemReward(110301612, 1),
                new ItemReward(113301578, 1),
                new ItemReward(111301550, 1),
                new ItemReward(112301491, 1),
                new ItemReward(114301616, 1),
                new ItemReward(125003577, 1),
                new ItemReward(120001068, 1),
                new ItemReward(122001205, 1),
                new ItemReward(121000978, 1),
                new ItemReward(123001067, 1),
                new ItemReward(120001068, 1),
                new ItemReward(122001205, 1),
                new ItemReward(101900646, 1),
                new ItemReward(101800647, 1),
                new ItemReward(101800658, 1),
                new ItemReward(101900659, 1),
                new ItemReward(187000024, 1),
                new ItemReward(167000959, 100),
                new ItemReward(167000964, 100),
                new ItemReward(190020087, 1),
                new ItemReward(166000190, 100),
                new ItemReward(188053787, 10),
                new ItemReward(188053788, 5),
                new ItemReward(188053789, 3)
            ));
        }});

        // RIDER
        CLASS_REWARDS.put(PlayerClass.RIDER, new HashMap<Race, List<ItemReward>>(){{
            put(Race.ELYOS, Arrays.asList(
                new ItemReward(110501011, 1),
                new ItemReward(113500989, 1),
                new ItemReward(111500986, 1),
                new ItemReward(112500935, 1),
                new ItemReward(114500998, 1),
                new ItemReward(120001060, 1),
                new ItemReward(122001197, 1),
                new ItemReward(121000970, 1),
                new ItemReward(123001059, 1),
                new ItemReward(120001060, 1),
                new ItemReward(122001197, 1),
                new ItemReward(125002335, 1),
                new ItemReward(102100573, 1),
                new ItemReward(101800643, 1),
                new ItemReward(101800654, 1),
                new ItemReward(102100584, 1),
                new ItemReward(187000024, 1),
                new ItemReward(167000954, 100),
                new ItemReward(167000962, 100),
                new ItemReward(190020087, 1),
                new ItemReward(166000190, 100),
                new ItemReward(188053787, 10),
                new ItemReward(188053788, 5),
                new ItemReward(188053789, 3)
            ));
            put(Race.ASMODIANS, Arrays.asList(
                new ItemReward(110501015, 1),
                new ItemReward(113500993, 1),
                new ItemReward(111500990, 1),
                new ItemReward(112500939, 1),
                new ItemReward(114501002, 1),
                new ItemReward(125002351, 1),
                new ItemReward(120001068, 1),
                new ItemReward(122001205, 1),
                new ItemReward(121000978, 1),
                new ItemReward(123001067, 1),
                new ItemReward(120001068, 1),
                new ItemReward(122001205, 1),
                new ItemReward(102100577, 1),
                new ItemReward(101800647, 1),
                new ItemReward(101800658, 1),
                new ItemReward(102100588, 1),
                new ItemReward(187000024, 1),
                new ItemReward(167000954, 100),
                new ItemReward(167000962, 100),
                new ItemReward(190020087, 1),
                new ItemReward(166000190, 100),
                new ItemReward(188053787, 10),
                new ItemReward(188053788, 5),
                new ItemReward(188053789, 3)
            ));
        }});

        // BARD
        CLASS_REWARDS.put(PlayerClass.BARD, new HashMap<Race, List<ItemReward>>(){{
            put(Race.ELYOS, Arrays.asList(
                new ItemReward(110101094, 1),
                new ItemReward(113101006, 1),
                new ItemReward(111100994, 1),
                new ItemReward(112100950, 1),
                new ItemReward(114101035, 1),
                new ItemReward(125002333, 1),
                new ItemReward(120001060, 1),
                new ItemReward(122001197, 1),
                new ItemReward(121000970, 1),
                new ItemReward(123001059, 1),
                new ItemReward(120001060, 1),
                new ItemReward(122001197, 1),
                new ItemReward(102000677, 1),
                new ItemReward(102000689, 1),
                new ItemReward(187000024, 1),
                new ItemReward(167000959, 100),
                new ItemReward(167000964, 100),
                new ItemReward(190020087, 1),
                new ItemReward(166000190, 100),
                new ItemReward(188053787, 10),
                new ItemReward(188053788, 5),
                new ItemReward(188053789, 3)
            ));
            put(Race.ASMODIANS, Arrays.asList(
                new ItemReward(110101098, 1),
                new ItemReward(113101010, 1),
                new ItemReward(111100998, 1),
                new ItemReward(112100954, 1),
                new ItemReward(114101039, 1),
                new ItemReward(125002349, 1),
                new ItemReward(120001068, 1),
                new ItemReward(122001205, 1),
                new ItemReward(121000978, 1),
                new ItemReward(123001067, 1),
                new ItemReward(120001068, 1),
                new ItemReward(122001205, 1),
                new ItemReward(102000681, 1),
                new ItemReward(102000694, 1),
                new ItemReward(187000024, 1),
                new ItemReward(167000959, 100),
                new ItemReward(167000964, 100),
                new ItemReward(190020087, 1),
                new ItemReward(166000190, 100),
                new ItemReward(188053787, 10),
                new ItemReward(188053788, 5),
                new ItemReward(188053789, 3)
            ));
        }});
    }

    private static class ItemReward {
        private final int itemId;
        private final int quantity;

        public ItemReward(int itemId, int quantity) {
            this.itemId = itemId;
            this.quantity = quantity;
        }

        public int getItemId() {
            return itemId;
        }

        public int getQuantity() {
            return quantity;
        }
    }

    public static void distributeClassChangeRewards(Player player, PlayerClass newClass, Race playerRace) {
        Map<Race, List<ItemReward>> raceRewards = CLASS_REWARDS.get(newClass);
        if (raceRewards != null) {
            List<ItemReward> itemRewards = raceRewards.get(playerRace);
            if (itemRewards != null) {
                for (ItemReward itemReward : itemRewards) {
                    ItemService.addItem(player, itemReward.getItemId(), itemReward.getQuantity());
                }
            }
        }
    }

   public static void changeClassToSelection(Player player, int dialogActionId) {
        PlayerClass selectedClass = getSelectedPlayerClass(player.getRace(), dialogActionId);
        if (selectedClass == null) return;

        // Allow class change for ALL classes
        boolean success = setClass(player, selectedClass, true, true);
        
        if (success) {
            Race playerRace = player.getRace();
            
            // Complete ALL race-specific quests for ALL classes
            if (playerRace == Race.ELYOS) {
                completeQuest(player, 1006);
                completeQuest(player, 1007);
                completeQuest(player, 1130);
                completeQuest(player, 1920);
                completeQuest(player, 1921);
                completeQuest(player, 1044);
                completeQuest(player, 10000);
                completeQuest(player, 10001);
                completeQuest(player, 10040);
                completeQuest(player, 10050);
                // Stigma Quests
                if (player.hasPermission(MembershipConfig.STIGMA_SLOT_QUEST)) {
                    completeQuest(player, 1929);
                }
            } else if (playerRace == Race.ASMODIANS) {
                completeQuest(player, 2008);
                completeQuest(player, 2009);
                completeQuest(player, 2200);
                completeQuest(player, 2945);
                completeQuest(player, 2946);
                completeQuest(player, 2042);
                completeQuest(player, 20000);
                completeQuest(player, 20001);
                completeQuest(player, 20040);
                completeQuest(player, 20050);
                // Stigma Quests
                if (player.hasPermission(MembershipConfig.STIGMA_SLOT_QUEST)) {
                    completeQuest(player, 2900);
                }
            }

            // Set ALL classes to max level
            PlayerCommonData commonData = player.getCommonData();
            commonData.setLevel(GSConfig.PLAYER_MAX_LEVEL);

            // Fully extend the cube for ALL classes
            if (CustomConfig.ENABLE_CUBE_MAX_LIMIT) {
                int targetCubeSize = CustomConfig.BASIC_CUBE_SIZE_LIMIT;
                int currentExpands = player.getNpcExpands();
                
                if (currentExpands < targetCubeSize) {
                    int neededExpansions = targetCubeSize - currentExpands;
                    for (int i = 0; i < neededExpansions; i++) {
                        player.getCommonData().setNpcExpands(currentExpands + i + 1);
                        player.setCubeLimit();
                        PacketSendUtility.sendPacket(player, SM_CUBE_UPDATE.cubeSize(StorageType.CUBE, player));
                    }
                    PacketSendUtility.sendMessage(player, "Your cube has been expanded to maximum size!");
                }
            }
            
            // Refresh skills for ALL classes
            ThreadPoolManager.getInstance().schedule(() -> {
                SkillLearnService.learnNewSkills(player, 9, player.getLevel());
            }, 3000);
        }
        PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(0, 0));
    }

    public static void completeAscensionQuest(Player player) {
        int questId = player.getRace() == Race.ELYOS ? 1006 : 2008;
        QuestState qs = player.getQuestStateList().getQuestState(questId);
        if (qs == null) {
            qs = new QuestState(questId, QuestStatus.COMPLETE);
            player.getQuestStateList().addQuest(questId, qs);
            PacketSendUtility.sendPacket(player, new SM_QUEST_ACTION(ActionType.ADD, qs));
        } else {
            qs.setStatus(QuestStatus.COMPLETE);
        }
        qs.setQuestVar(0);
        qs.setRewardGroup(0);
        PacketSendUtility.sendPacket(player, new SM_QUEST_ACTION(ActionType.UPDATE, qs));
    }
	public static void completeQuest(Player player, int questId) {
        QuestState qs = player.getQuestStateList().getQuestState(questId);
        if (qs == null) {
            qs = new QuestState(questId, QuestStatus.COMPLETE);
            player.getQuestStateList().addQuest(questId, qs);
            PacketSendUtility.sendPacket(player, new SM_QUEST_ACTION(ActionType.ADD, qs));
        } else {
            qs.setStatus(QuestStatus.COMPLETE);
        }
        qs.setQuestVar(0);
        qs.setRewardGroup(0);
        PacketSendUtility.sendPacket(player, new SM_QUEST_ACTION(ActionType.UPDATE, qs));
    }


    public static boolean setClass(Player player, PlayerClass newClass) {
        return setClass(player, newClass, true, false);
    }

    public static boolean setClass(Player player, PlayerClass newClass, boolean validate, boolean updateDaevaStatus) {
        if (newClass == null)
            return false;

        if (validate) {
            PlayerClass oldClass = player.getPlayerClass();
            if (!oldClass.isStartingClass()) {
                PacketSendUtility.sendMessage(player, "You already switched class");
                return false;
            }
            byte id = oldClass.getClassId();
            if (oldClass == newClass || newClass.getClassId() <= id || newClass.getClassId() > id + 2) {
                PacketSendUtility.sendMessage(player, "Invalid class chosen");
                return false;
            }
        }

        player.getCommonData().setPlayerClass(newClass);
        player.getGameStats().updateStatsTemplate();
        player.getController().upgradePlayer();
        PacketSendUtility.broadcastPacket(player, new SM_ACTION_ANIMATION(player.getObjectId(), ActionAnimation.CLASS_CHANGE, player.getLevel()), true);
        PacketSendUtility.broadcastPacket(player, new SM_PLAYER_INFO(player));
        SkillLearnService.learnNewSkills(player, 9, player.getLevel());

        if (updateDaevaStatus) {
            if (!newClass.isStartingClass()) {
                completeAscensionQuest(player);
                player.getCommonData().updateDaeva();
            } else {
                player.getCommonData().setDaeva(false);
            }
        }
        
        // Distribute rewards for ALL classes
        distributeClassChangeRewards(player, newClass, player.getRace());

        return true;
    }
	

    // Rest of original methods unchanged
    public static int getClassSelectionDialogPageId(Race playerRace, PlayerClass playerClass) {
        switch (playerClass) {
            case WARRIOR: return playerRace == Race.ELYOS ? 2375 : 3057;
            case SCOUT: return playerRace == Race.ELYOS ? 2716 : 3398;
            case MAGE: return playerRace == Race.ELYOS ? 3057 : 3739;
            case PRIEST: return playerRace == Race.ELYOS ? 3398 : 4080;
            case ENGINEER: return playerRace == Race.ELYOS ? 3739 : 3569;
            case ARTIST: return playerRace == Race.ELYOS ? 4080 : 3910;
            default: return 0;
        }
    }

    public static PlayerClass getSelectedPlayerClass(Race race, int dialogActionId) {
        switch (race) {
            case ELYOS:
                switch (dialogActionId) {
                    case SELECT5_1: return PlayerClass.GLADIATOR;
                    case SELECT5_2: return PlayerClass.TEMPLAR;
                    case SELECT6_1: return PlayerClass.ASSASSIN;
                    case SELECT6_2: return PlayerClass.RANGER;
                    case SELECT7_1: return PlayerClass.SORCERER;
                    case SELECT7_2: return PlayerClass.SPIRIT_MASTER;
                    case SELECT8_1: return PlayerClass.CLERIC;
                    case SELECT8_2: return PlayerClass.CHANTER;
                    case SELECT9_1: return PlayerClass.GUNNER;
                    case SELECT9_2: return PlayerClass.RIDER;
                    case SELECT10_1: return PlayerClass.BARD;
                }
                break;
            case ASMODIANS:
                switch (dialogActionId) {
                    case SELECT7_1: return PlayerClass.GLADIATOR;
                    case SELECT7_2: return PlayerClass.TEMPLAR;
                    case SELECT8_1: return PlayerClass.ASSASSIN;
                    case SELECT8_2: return PlayerClass.RANGER;
                    case SELECT9_1: return PlayerClass.SORCERER;
                    case SELECT9_2: return PlayerClass.SPIRIT_MASTER;
                    case SELECT10_1: return PlayerClass.CLERIC;
                    case SELECT10_2: return PlayerClass.CHANTER;
                    case SELECT8_3_1: return PlayerClass.GUNNER;
                    case SELECT8_3_2: return PlayerClass.RIDER;
                    case SELECT9_3_1: return PlayerClass.BARD;
                }
        }
        return null;
    }
}