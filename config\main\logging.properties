#

# Send server errors and warnings to a Discord channel (see logback.xml for further configuration)
# Default: empty (deactivated)
gameserver.log.status.discord.webhook_url =
# The avatar url is inserted in the conversion pattern in logback.xml and therefore can use Logback variables, such as %level (see
# http://logback.qos.ch/manual/layouts.html#conversionWord) to set different avatars depending on the severity.
gameserver.log.status.discord.avatar_url =

# Enable audit logging
# Default: true
gameserver.log.audit = true

# Enable craft logging
# Default: true
gameserver.log.craft = false

# Enable player exchange logging
# Default: false
gameserver.log.player.exchange = false

# Enable Broker exchange logging
# Default: false
gameserver.log.broker.exchange = false

# Enable gameshop logging
# Default: false
gameserver.log.ingameshop = false

# Enable gameshop logging into database
# Default: false
gameserver.log.ingameshop.sql = false

# Enable gmaudit logging (chat commands, gm whispers, ...)
# Default: true
gameserver.log.gmaudit = true

# Enable general chat logging (public, group, shout, ...)
# Default: true
gameserver.log.chats.general = true

# Enable private chat logging (legion & whisper)
# Default: false
gameserver.log.chats.private = false

# Enable item logging
# Default: true
gameserver.log.item = false

# Enable kill logging
# Default: false
gameserver.log.kill = false

# Enable PowerLeveling logging
# Default: false
gameserver.log.pl = false

# Enable mail logging
# Default: false
gameserver.log.mail = false

# Enable siege logging
# Default: false
gameserver.log.siege = false

# Enable sysmail logging
# Default: false
gameserver.log.sysmail = false

# Enable house auction result logging
# Default: true
gameserver.log.auction = true

# Enable tampering log
# Default: false
gameserver.log.tampering = false