<?xml version="1.0" standalone="yes"?>
<xs:schema version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:element name="fortress" type="fortress"/>
	<xs:element name="agent_fight" type="agent_fight"/>
	<xs:element name="siege_schedule" type="siegeSchedule"/>
	<xs:complexType name="siegeSchedule">
		<xs:sequence>
			<xs:choice maxOccurs="unbounded">
				<xs:element ref="fortress" minOccurs="1" maxOccurs="unbounded"/>
				<xs:element ref="agent_fight" minOccurs="1" maxOccurs="unbounded"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="fortress">
		<xs:sequence>
			<xs:element name="siegeTime" type="xs:string" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attribute name="id" type="xs:int" use="required"/>
	</xs:complexType>
	<xs:complexType name="agent_fight">
		<xs:sequence>
			<xs:element name="siegeTime" type="xs:string" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attribute name="id" type="xs:int" use="required"/>
	</xs:complexType>
</xs:schema>