#
# ----------------------------
# World config's:
# ----------------------------

# World region size
# Default: 128
# Only use: 64,128,256 If use other value mapRegion system dont's work.
gameserver.world.region.size = 128

# Max number of usual twin count. If set to 0, values from world_maps.xml are used
# Default: 1 - only 1 default twin is used for each map
gameserver.world.max.twincount.usual = 1

# Max number of beginners twin count. If set to 0, values from world_maps.xml are used
# Default: -1 - disabled
gameserver.world.max.twincount.beginner = -1

# Emulate FastTrack by using world beginner twins. Have to enable
# gameserver.world.max.twincount.beginner to make it working
# Default: true
gameserver.world.emulate.fasttrack = true

# Location of zone *.java handlers
gameserver.world.zone_handler_directory = ./data/handlers/zone


# Comma-separated list of map IDs where teleport is blocked210050000,210070000,220080000,220070000,220100000,210090000
gameserver.world.disabled.ids= 