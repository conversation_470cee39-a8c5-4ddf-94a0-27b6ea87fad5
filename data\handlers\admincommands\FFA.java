package admincommands;

import com.aionemu.gameserver.configs.main.CustomConfig;
import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.services.FFAService;
import com.aionemu.gameserver.utils.chathandlers.AdminCommand;
import com.aionemu.gameserver.world.World;

/**
 * FFA Event Admin Commands
 * 
 * Provides comprehensive admin control over the FFA event system:
 * - Event status monitoring
 * - Force start/stop events
 * - View leaderboards and statistics
 * - Manage participants
 * - View configuration
 * 
 * <AUTHOR> System
 */
public class FFA extends AdminCommand {

    public FFA() {
        super("ffaadmin", "Manages FFA events.");
        
        // @formatter:off
        setSyntaxInfo(
            "<status> - Show current event status",
            "<start> - Force start FFA event",
            "<stop> - Force stop FFA event",
            "<leaderboard> [count] - Show leaderboard (default: 10)",
            "<participants> - List current participants",
            "<fixdeath> <player> - Fix player stuck in death state",
            "<info> - Show configuration and settings"
        );
        // @formatter:on
    }

    @Override
    public void execute(Player admin, String... params) {
        if (params.length == 0) {
            sendInfo(admin);
            return;
        }

        FFAService service = FFAService.getInstance();
        String command = params[0].toLowerCase();

        switch (command) {
            case "status":
                showStatus(admin, service);
                break;
                
            case "start":
                forceStartEvent(admin, service);
                break;
                
            case "stop":
                forceStopEvent(admin, service);
                break;
                
            case "leaderboard":
                int count = 10; // default
                if (params.length > 1) {
                    try {
                        count = Integer.parseInt(params[1]);
                        count = Math.max(1, Math.min(50, count)); // limit 1-50
                    } catch (NumberFormatException e) {
                        sendInfo(admin, "Invalid number format for leaderboard count.");
                        return;
                    }
                }
                showLeaderboard(admin, service, count);
                break;
                
            case "participants":
                showParticipants(admin, service);
                break;
                
            case "info":
                showConfiguration(admin, service);
                break;
                
            case "fixdeath":
                if (params.length < 2) {
                    sendInfo(admin, "Usage: //ffa fixdeath <player>");
                    return;
                }
                fixPlayerDeathState(admin, service, params[1]);
                break;
                
            default:
                sendInfo(admin);
                break;
        }
    }

    private void showStatus(Player admin, FFAService service) {
        StringBuilder status = new StringBuilder();
        status.append("=== FFA Event Status ===\n");
        status.append("System Enabled: ").append(CustomConfig.FFA_ENABLED ? "YES" : "NO").append("\n");
        
        if (CustomConfig.FFA_ENABLED) {
            status.append("Event Active: ").append(service.isEventActive() ? "YES" : "NO").append("\n");
            
            if (service.isEventActive()) {
                status.append("Participants: ").append(service.getParticipantCount()).append("/").append(CustomConfig.FFA_MAX_PLAYERS).append("\n");
                status.append("Current Map: ").append(getMapName(service.getCurrentEventMapId())).append(" (").append(service.getCurrentEventMapId()).append(")\n");
                status.append("Event Duration: ").append(CustomConfig.FFA_DURATION_MINUTES).append(" minutes\n");
            } else {
                status.append("Auto Start: ").append(CustomConfig.FFA_AUTO_START ? "YES" : "NO").append("\n");
                if (CustomConfig.FFA_AUTO_START && !CustomConfig.FFA_SCHEDULE.isEmpty()) {
                    status.append("Schedule: ").append(CustomConfig.FFA_SCHEDULE).append("\n");
                }
            }
        }
        
        sendInfo(admin, status.toString());
    }

    private void forceStartEvent(Player admin, FFAService service) {
        if (!CustomConfig.FFA_ENABLED) {
            sendInfo(admin, "FFA system is disabled in configuration.");
            return;
        }

        if (service.isEventActive()) {
            sendInfo(admin, "FFA event is already active.");
            return;
        }

        service.forceStartEvent();
        sendInfo(admin, "FFA event has been force started.");
    }

    private void forceStopEvent(Player admin, FFAService service) {
        if (!service.isEventActive()) {
            sendInfo(admin, "No FFA event is currently active.");
            return;
        }

        service.forceEndEvent();
        sendInfo(admin, "FFA event has been force stopped.");
    }

    private void showLeaderboard(Player admin, FFAService service, int count) {
        StringBuilder leaderboard = service.getLeaderboard(count);
        if (leaderboard.length() > 0) {
            sendInfo(admin, leaderboard.toString());
        } else {
            sendInfo(admin, "No FFA statistics available yet.");
        }
    }

    private void showParticipants(Player admin, FFAService service) {
        if (!service.isEventActive()) {
            sendInfo(admin, "No FFA event is currently active.");
            return;
        }

        StringBuilder participants = service.getCurrentParticipants();
        if (participants.length() > 0) {
            sendInfo(admin, participants.toString());
        } else {
            sendInfo(admin, "No participants in the current FFA event.");
        }
    }

    private void showConfiguration(Player admin, FFAService service) {
        StringBuilder config = new StringBuilder();
        config.append("=== FFA Configuration ===\n");
        config.append("Enabled: ").append(CustomConfig.FFA_ENABLED).append("\n");
        config.append("Auto Start: ").append(CustomConfig.FFA_AUTO_START).append("\n");
        config.append("Schedule: ").append(CustomConfig.FFA_SCHEDULE.isEmpty() ? "None" : CustomConfig.FFA_SCHEDULE).append("\n");
        config.append("Duration: ").append(CustomConfig.FFA_DURATION_MINUTES).append(" minutes\n");
        config.append("Max Players: ").append(CustomConfig.FFA_MAX_PLAYERS).append("\n");
        config.append("Min Level: ").append(CustomConfig.FFA_MIN_LEVEL).append("\n");
        config.append("Available Maps: ").append(CustomConfig.FFA_MAPS).append("\n");
        config.append("Rewards: ").append(CustomConfig.FFA_REWARDS).append("\n");
        
        sendInfo(admin, config.toString());
    }

    private void fixPlayerDeathState(Player admin, FFAService service, String playerName) {
        if (!service.isEventActive()) {
            sendInfo(admin, "No FFA event is currently active.");
            return;
        }

        Player target = World.getInstance().getPlayer(playerName);
        if (target == null) {
            sendInfo(admin, "Player '" + playerName + "' not found or not online.");
            return;
        }

        if (!service.isParticipant(target)) {
            sendInfo(admin, "Player '" + playerName + "' is not participating in the FFA event.");
            return;
        }

        if (!target.getLifeStats().isDead()) {
            sendInfo(admin, "Player '" + playerName + "' is not dead.");
            return;
        }

        service.manualResurrectPlayer(target);
        sendInfo(admin, "Attempting to fix death state for player '" + playerName + "'.");
    }

    private String getMapName(int mapId) {
        switch (mapId) {
            case 300030000:
                return "Nochsana Training Camp";
            case 300040000:
                return "Dark Poeta";
            case 300250000:
                return "Esoterrace";
            default:
                return "Unknown Map";
        }
    }
}
