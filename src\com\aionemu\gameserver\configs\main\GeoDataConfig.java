package com.aionemu.gameserver.configs.main;

import com.aionemu.commons.configuration.Property;

public class GeoDataConfig {

	/**
	 * Geodata enable
	 */
	@Property(key = "gameserver.geodata.enable", defaultValue = "true")
	public static boolean GEO_ENABLE;

	/**
	 * Enable canSee checks using geodata.
	 */
	@Property(key = "gameserver.geodata.cansee.enable", defaultValue = "true")
	public static boolean CANSEE_ENABLE;

	/**
	 * Enable Fear skill using geodata.
	 */
	@Property(key = "gameserver.geodata.fear.enable", defaultValue = "true")
	public static boolean FEAR_ENABLE;

	/**
	 * Enable Geo checks during npc movement (prevent flying mobs)
	 */
	@Property(key = "gameserver.geodata.npc.move", defaultValue = "true")
	public static boolean GEO_NPC_MOVE;

	/**
	 * Enable geo materials using skills
	 */
	@Property(key = "gameserver.geodata.materials.enable", defaultValue = "true")
	public static boolean GEO_MATERIALS_ENABLE;

	/**
	 * Show collision zone name and skill id
	 */
	@Property(key = "gameserver.geodata.materials.showdetails", defaultValue = "false")
	public static boolean GEO_MATERIALS_SHOWDETAILS;

	/**
	 * Enable geo shields
	 */
	@Property(key = "gameserver.geodata.shields.enable", defaultValue = "true")
	public static boolean GEO_SHIELDS_ENABLE;

	/**
	 * Enhanced position validation for skill effects and teleportation
	 */
	@Property(key = "gameserver.geodata.position.validation.enable", defaultValue = "true")
	public static boolean POSITION_VALIDATION_ENABLE;

	/**
	 * Maximum Z-coordinate search range for ground detection (in meters)
	 */
	@Property(key = "gameserver.geodata.position.validation.max_z_range", defaultValue = "10.0")
	public static float POSITION_VALIDATION_MAX_Z_RANGE;

	/**
	 * Minimum safe distance from collision surfaces (in meters)
	 */
	@Property(key = "gameserver.geodata.position.validation.safe_distance", defaultValue = "0.5")
	public static float POSITION_VALIDATION_SAFE_DISTANCE;

	/**
	 * Enable fallback to original position if no safe ground is found
	 */
	@Property(key = "gameserver.geodata.position.validation.fallback_enabled", defaultValue = "true")
	public static boolean POSITION_VALIDATION_FALLBACK_ENABLED;

	/**
	 * Enable logging of position validation failures for debugging
	 */
	@Property(key = "gameserver.geodata.position.validation.debug_logging", defaultValue = "false")
	public static boolean POSITION_VALIDATION_DEBUG_LOGGING;

}
