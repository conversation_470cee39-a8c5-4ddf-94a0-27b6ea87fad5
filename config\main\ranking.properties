#
# ----------------------------
# Ranking config's:
# ----------------------------

# <PERSON><PERSON> expression for update rule
# Default to: 0 0 0 ? * * - fire every day in 0:00:00
gameserver.topranking.updaterule = 0 0 0 ? * *

gameserver.topranking.daily.gploss.time = 0 0 12 ? * *

# Number of legions that will appear in the legion ranking list
# Default: 50
gameserver.topranking.legion_limit = 50

# Abyssrank limitation for inactive players
# If this value is higher than zero, only players,
# who were active in the last X days are possible candidates
# Default: 0
gameserver.topranking.max.offline.days = 0

# The minimum allowed abyss rank to receive XFORM skills.
# Valid min ranks are as follows (min to max):
# STAR5_OFFICER -> GENERAL -> GREAT_GENERAL -> COMMANDER -> SUPREME_COMMANDER
# Default: STAR5_OFFICER
gameserver.topranking.xform.min_rank = STAR5_OFFICER

# Maximum quota of players for each rank (largest number also controls the maximum number of players that will appear in the ranking list)
gameserver.topranking.quota.STAR1_OFFICER = 1000
gameserver.topranking.quota.STAR2_OFFICER = 700
gameserver.topranking.quota.STAR3_OFFICER = 500
gameserver.topranking.quota.STAR4_OFFICER = 300
gameserver.topranking.quota.STAR5_OFFICER = 100
gameserver.topranking.quota.GENERAL = 30
gameserver.topranking.quota.GREAT_GENERAL = 10
gameserver.topranking.quota.COMMANDER = 3
gameserver.topranking.quota.SUPREME_COMMANDER = 1

# Daily GP loss for each rank
# Missing ranks will have zero GP loss per day
gameserver.topranking.gp_loss.STAR1_OFFICER = 9
gameserver.topranking.gp_loss.STAR2_OFFICER = 19
gameserver.topranking.gp_loss.STAR3_OFFICER = 31
gameserver.topranking.gp_loss.STAR4_OFFICER = 51
gameserver.topranking.gp_loss.STAR5_OFFICER = 150
gameserver.topranking.gp_loss.GENERAL = 171
gameserver.topranking.gp_loss.GREAT_GENERAL = 176
gameserver.topranking.gp_loss.COMMANDER = 190
gameserver.topranking.gp_loss.SUPREME_COMMANDER = 219