#
# ----------------------------
# Admin Commands:
# (depends on access level)
# ----------------------------
access = 9
add = 8
addcube = 8
addemotion = 8
devilsmark = 9
mixfight = 9
ffaadmin = 9
1v1 = 9
addexp = 8
addset = 8
addskill = 8
addtitle = 8
ahserion = 9
ai = 9
alternpc = 9
announce = 1
announcements = 9
auction = 9
ban = 9
banchar = 9
banip = 9
banmac = 9
banhdd = 9
cinstance = 9
unbanmac = 9
base = 8
bk = 1
changerace = 1
collide = 9
configure = 9
cooldown = 9
coords = 1
damage = 9
debug = 9
delete = 8
delskill = 8
dispel = 5
dropinfo = 5
dye = 7
enemy = 1
energy = 9
equip = 9
event = 1
fixpath = 9
fsc = 9
gag = 5
gameshop = 9
goto = 1
grant = 9
headhunting = 9
heal = 5
house = 9
html = 9
info = 9
instance = 9
invasion = 9
invis = 1
invul = 1
kick = 6
kill = 7
legion = 9
map = 9
megaphone = 9
worldraid = 9
morph = 1
motion = 9
moveto = 1
moveplayertoplayer = 9
movetome = 1
movetoobj = 9
movie = 9
npcskill = 9
online = 1
passkeyreset = 9
pet = 9
playerinfo = 7
quest = 6
ranking = 9
relinquishcraft = 8
reload = 9
remove = 8
removecd = 9
rename = 8
res = 5
rift = 9
rprison = 9
say = 9
see = 1
send = 9
set = 8
siege = 9
spawn = 8
spawnu = 9
spawnAssembledNpc = 9
speed = 1
sprison = 9
stat = 9
state = 9
stoken = 9
sys = 9
sysmail = 8
teleportation = 7
time = 9
unban = 9
unbanchar = 9
unbanip = 9
useskill = 9
whisper = 1
weather = 9
zone = 9

# ----------------------------
# Player Commands:
# (depends on membership)
# ----------------------------
advent = 0
buy = 0
decompose = 0
del = 0
faction = 10
gmlist = 0
help = 0
id = 0
lock = 10
noexp = 0
nomorph = 0
preview = 0
pvp = 0
ffa = 0
questrestart = 10

# event commands (levels will be overridden during event via <config_properties/> from events.xml)
easter = 10
symphony = 10

# ----------------------------
# Console Commands:
# (depends on access level)
# ----------------------------
Bookmark_add = 9
addcskill = 9
addquest = 9
attrbonus = 9
changeclass = 9
classup = 9
clearusercoolt = 9
combineskill = 9
delete_items = 9
deletecquest = 9
deleteskill = 9
endquest = 9
givetitle = 9
guild = 9
inventory = 9
invisible = 2
itemcooltime = 9
leveldown = 9
levelup = 9
partyrecall = 9
remove_skill_delay_all = 9
resurrect = 9
search = 9
set_enchantcount = 9
set_makeup_bonus = 9
set_vitalpoint = 9
setinventorygrowth = 9
skill = 9
status = 9
teleport = 9
teleport_to_named = 9
teleportto = 9
visible = 2
wish = 9
wishid = 9

# Location of chat command *.java handlers
gameserver.commands.handler_directories = ./data/handlers/admincommands, ./data/handlers/playercommands, ./data/handlers/consolecommands