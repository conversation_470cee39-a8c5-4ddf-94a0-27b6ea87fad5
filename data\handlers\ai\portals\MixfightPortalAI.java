package ai.portals;

import com.aionemu.gameserver.ai.AIName;
import com.aionemu.gameserver.model.ChatType;
import com.aionemu.gameserver.model.gameobjects.Npc;
import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.services.MixfightService;
import com.aionemu.gameserver.utils.PacketSendUtility;
import com.aionemu.gameserver.utils.ThreadPoolManager;  // Added import

import ai.ActionItemNpcAI;

import java.util.Map;                   // Added import
import java.util.concurrent.ConcurrentHashMap;  // Added import
import java.util.concurrent.ScheduledFuture;    // Added import

@AIName("mixfight_portal")
public class MixfightPortalAI extends ActionItemNpcAI {

    // Cooldown period in seconds (10 seconds)
    private static final int JOIN_COOLDOWN = 10;
    
    // Track join cooldowns per player
    private static final Map<Integer, ScheduledFuture<?>> cooldownMap = new ConcurrentHashMap<>();

    public MixfightPortalAI(Npc owner) {
        super(owner);
    }

    @Override
    protected void handleUseItemFinish(Player player) {
        MixfightService mixfightService = MixfightService.getInstance();

        if (mixfightService == null) {
            PacketSendUtility.sendMessage(player, "MixFight service is currently unavailable.", ChatType.BRIGHT_YELLOW_CENTER);
            return;
        }

        // Check if player is on cooldown
        if (cooldownMap.containsKey(player.getObjectId())) {
            PacketSendUtility.sendMessage(player, 
                "Please wait before attempting to join or leave again.", 
                ChatType.BRIGHT_YELLOW_CENTER);
            return;
        }

        // Check if player is already participating
        if (mixfightService.isParticipating(player)) {
            handleLeaveEvent(player, mixfightService);
        } else {
            handleJoinEvent(player, mixfightService);
        }
    }

    private void handleJoinEvent(Player player, MixfightService mixfightService) {
        if (player.getLevel() < 50) {
            PacketSendUtility.sendMessage(player,
                "You must be at least level 50 to participate in MixFight.",
                ChatType.BRIGHT_YELLOW_CENTER);
            return;
        }

        showEventInfo(player, mixfightService);

        boolean success = mixfightService.joinEvent(player);
        if (success) {
            // Apply cooldown after successful join
            applyCooldown(player);
        } else {
            PacketSendUtility.sendMessage(player,
                "Failed to join MixFight event. Check the requirements or try again later.",
                ChatType.BRIGHT_YELLOW_CENTER);
        }
    }

    private void handleLeaveEvent(Player player, MixfightService mixfightService) {
        boolean success = mixfightService.leaveEvent(player);
        if (success) {
            // Apply cooldown after successful leave
            applyCooldown(player);
        } else {
            PacketSendUtility.sendMessage(player,
                "Failed to leave MixFight event.",
                ChatType.BRIGHT_YELLOW_CENTER);
        }
    }

    private void applyCooldown(Player player) {
        int playerId = player.getObjectId();
        
        // Remove any existing cooldown
        if (cooldownMap.containsKey(playerId)) {
            cooldownMap.get(playerId).cancel(true);
        }
        
        // Schedule new cooldown
        ScheduledFuture<?> task = ThreadPoolManager.getInstance().schedule(() -> {
            cooldownMap.remove(playerId);
        }, JOIN_COOLDOWN * 1000);  // Convert to milliseconds
        
        cooldownMap.put(playerId, task);
    }

    private void showEventInfo(Player player, MixfightService mixfightService) {
        String eventStatus = mixfightService.getEventStatus();
        int participantCount = mixfightService.getParticipantCount();

        PacketSendUtility.sendMessage(player,
            String.format("MixFight Event - Status: %s | Participants: %d",
                eventStatus, participantCount),
            ChatType.BRIGHT_YELLOW_CENTER);

        PacketSendUtility.sendMessage(player,
            "Cross-faction PvP event. Fight all participants for points and rewards!",
            ChatType.BRIGHT_YELLOW_CENTER);
    }
}