#
# ----------------------------
# Rate config's:
# ----------------------------

# NOTE: All values can be comma separated int or float values.
#       This way you can define rates for any number of membership
#       types you want to support.
#       The index of each entry corresponds to one membership level.
#       Therefore "1, 1.5, 2" means membership 0 uses x1 rates,
#       membership 1 x1.5, membership 2 x2 and so on.
#
#       By default, this config contains rates for two membership
#       levels: 0 = Regular, 1 = Premium

# ----------------------------
# Crafting chances:
# ----------------------------
# Chance in percent to have a critical procraft (applied on first step)
# Default: 15.0, 30.0
gameserver.rates.crafting.crit_chances = 15.0, 30.0

# Chance in percent to have a combo procraft (applied on subsequent steps)
# Default: 25.0, 50.0
gameserver.rates.crafting.combo_crit_chances = 25.0, 50.0

# ----------------------------
# Enchantment chances:
# ----------------------------
# Base manastone chance in percent
# Default: 75.0, 75.0
gameserver.rates.manastone_chances = 75.0, 75.0

# Enchanting base chance in percent, if the stone exactly matches the item level and quality (will divert according to the difference)
# Default: 65.0, 65.0
gameserver.rates.enchantment_stone.base_chances = 65.0, 65.0

# Enchanting chance in percent if the item is amplified (this rate is fixed)
# Default: 50.0, 50.0
gameserver.rates.enchantment_stone.amplified_chances = 50.0, 50.0

# Tempering chance in percent (except for plumes)
# Default: 65.0, 65.0
gameserver.rates.tampering_chances = 65.0, 65.0

# ----------------------------
# Level XP rates:
# ----------------------------
# Solo PvE XP
# Default: 1.0, 2.0
gameserver.rates.xp.solo = 1.0, 2.0

# Group PvE XP
# Default: 1.0, 2.0
gameserver.rates.xp.group = 1.0, 2.0

# Quest XP
# Default: 1.0, 2.0
gameserver.rates.xp.quest = 1.0, 2.0

# Gathering XP
# Default: 1.0, 2.0
gameserver.rates.xp.gathering = 1.0, 2.0

# Crafting XP
# Default: 1.0, 2.0
gameserver.rates.xp.crafting = 1.0, 2.0

# PvP XP
# Default: 1.0, 2.0
gameserver.rates.xp.pvp = 1.0, 2.0

# ----------------------------
# Skill level XP rates:
# ----------------------------
# Gathering Skill XP
# Default: 1.0, 2.0
gameserver.rates.skill_xp.gathering = 1.0, 2.0

# Crafting Skill XP
# Default: 1.0, 2.0 
gameserver.rates.skill_xp.crafting = 1.0, 2.0

# ----------------------------
# AP rates:
# ----------------------------
# PvP AP Gain 
# Default: 1.0, 2.0
gameserver.rates.ap.pvp.gain = 1.0, 2.0

# PvP AP Loss 
# Default: 1.0, 1.0
gameserver.rates.ap.pvp.loss = 1.0, 1.0

# PvE AP
# Default: 1.0, 2.0
gameserver.rates.ap.pve = 1.0, 2.0

# Quest AP
# Default: 1.0, 2.0
gameserver.rates.ap.quest = 1.0, 2.0

# Dredgion AP
# Default: 1.0, 2.0
gameserver.rates.ap.dredgion = 1.0, 2.0

# ----------------------------
# GP rates:
# ----------------------------
# GP Gain 
# Default: 1.0, 2.0
gameserver.rates.gp.gain = 1.0, 2.0

# ----------------------------
# DP rates:
# ----------------------------
# PvE DP
# Default: 1.0, 2.0
gameserver.rates.dp.pve = 1.0, 2.0

# PvP DP
# Default: 1.0, 2.0
gameserver.rates.dp.pvp = 1.0, 2.0

# ----------------------------
# Quest Kinah rates:
# ----------------------------
# Regular
# Default: 1.0, 2.0
gameserver.rates.kinah.quest = 1.0, 2.0

# ----------------------------
# Drop rates:
# ----------------------------
# Drop
# Default: 1.0, 2.0
gameserver.rates.drop = 1.0, 2.0

# ----------------------------
# Gathering count rates:
# ----------------------------
# Gathering count multiplier
# Default: 1.0, 2.0
gameserver.rates.gathering.count = 1.0, 2.0

# ----------------------------
# PvP Arena reward rates:
# ----------------------------
# PvP Discipline Arena Reward rates
# Default: 1.0, 2.0
gameserver.rates.pvparena.discipline = 1.0, 2.0

# PvP Chaos Arena Reward rates
# Default: 1.0, 2.0
gameserver.rates.pvparena.chaos = 1.0, 2.0

# PvP Harmony Arena AP/Crucible Insignia rates
# Default: 1.0, 2.0
gameserver.rates.pvparena.harmony = 1.0, 2.0

# PvP Glory Arena AP/GP rates
# Default: 1.0, 2.0
gameserver.rates.pvparena.glory = 1.0, 2.0

# ----------------------------
# Sell Limit rates:
# ----------------------------
# Regular
# Default: 1.0, 2.0
gameserver.rates.sell_limit = 1.0, 2.0
