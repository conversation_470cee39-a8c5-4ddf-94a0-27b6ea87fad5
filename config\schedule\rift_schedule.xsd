<?xml version="1.0" standalone="yes"?>
<xs:schema version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:element name="rift" type="rift"/>
	<xs:element name="rift_schedule" type="riftSchedule"/>
	<xs:complexType name="riftSchedule">
		<xs:sequence>
			<xs:element ref="rift" minOccurs="1" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="rift">
		<xs:sequence>
			<xs:element name="open" type="OpenRift" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attribute name="id" type="xs:int" use="required"/>
	</xs:complexType>
	<xs:complexType name="OpenRift">
		<xs:attribute name="spawn" type="xs:boolean" use="optional"/>
		<xs:attribute name="schedule" type="xs:string" use="required"/>
	</xs:complexType>
</xs:schema>