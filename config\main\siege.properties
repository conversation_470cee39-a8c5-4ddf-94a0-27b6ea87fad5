#
# ----------------------------
# Basic siege config's:
# ----------------------------

# Enable siege system
# Default: true
gameserver.siege.enable = true

# Enable automatic Balaur Assaults
gameserver.siege.assault.enable = true

# Rate for Balaur assaults chance(float)
# Default: 1
gameserver.siege.assault.rate = 1

# Moltenus spawn time
# Default: 0 0 22 ? * SUN
gameserver.moltenus.time = 0 0 22 ? * SUN

# Legendary npc's health multiplier (1.0 = 100%, 0.5 = 50%)
# Default: 1.0
gameserver.siege.health.multiplier = 1.0

# Siege and Assault difficulty multiplier
# Default: 1.0
gameserver.siege.difficulty.multiplier = 1.0

# Panesterra player limit per faction
# Default: 100
gameserver.siege.panesterra.maxplayers = 100

# Ahserion's Flight player limit per faction
# Default: 100
gameserver.siege.panesterra.ahserion.maxplayers = 100

# Ahser<PERSON>'s Flight schedule
# Default: 0 50 18 ? * *
gameserver.siege.panesterra.ahserion.time = 0 50 18 ? * SUN

# Heal amount(%) recovered with each activation
# Default: 0.01
gameserver.siege.door.repair.heal.percent = 0.01

# Enables/Disables rewards for players if balaur succeed in sieges
# Default: false
gameserver.siege.reward.balaur.victory = false

# Ignore staff members on location clear
# Default: false
gameserver.siege.ignore_staff_on_location_clear = false