package data.handlers.admincommands;

import com.aionemu.gameserver.dataholders.DataManager;
import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.model.templates.item.ItemTemplate;
import com.aionemu.gameserver.model.templates.item.actions.SkillUseAction;
import com.aionemu.gameserver.model.templates.item.actions.ItemActions;
import com.aionemu.gameserver.skillengine.SkillEngine;
import com.aionemu.gameserver.skillengine.model.Skill;
import com.aionemu.gameserver.utils.PacketSendUtility;
import com.aionemu.gameserver.utils.chathandlers.AdminCommand;

/**
 * Admin command for testing skill effects that move players.
 * This command helps verify that movement-based skill effects
 * properly use position validation to prevent falling through terrain.
 * 
 * Usage:
 * //testskill flash - Test flash/teleport skills
 * //testskill pull - Test pull effects (requires target)
 * //testskill custom <skillId> - Test specific skill by ID
 * 
 * <AUTHOR> by AI Assistant
 */
public class TestSkillEffects extends AdminCommand {

    public TestSkillEffects() {
        super("testskill");
    }

    @Override
    public void execute(Player player, String... params) {
        if (params.length == 0) {
            showHelp(player);
            return;
        }

        switch (params[0].toLowerCase()) {
            case "flash":
                testFlashSkills(player);
                break;
            case "pull":
                testPullSkills(player);
                break;
            case "custom":
                if (params.length >= 2) {
                    testCustomSkill(player, params[1]);
                } else {
                    PacketSendUtility.sendMessage(player, "Usage: //testskill custom <skillId>");
                }
                break;
            case "help":
            default:
                showHelp(player);
                break;
        }
    }

    private void testFlashSkills(Player player) {
        PacketSendUtility.sendMessage(player, "=== Testing Flash/Teleport Skills ===");
        
        // Common flash skill IDs (these may vary by server)
        int[] flashSkillIds = {
            1573, // Flash (Assassin)
            1574, // Flash II (Assassin)
            1575, // Flash III (Assassin)
            2345, // Spatial Warp (Sorcerer)
            2346, // Spatial Warp II (Sorcerer)
        };
        
        PacketSendUtility.sendMessage(player, "Testing common flash skills...");
        PacketSendUtility.sendMessage(player, "Position before: " + player.getX() + ", " + player.getY() + ", " + player.getZ());
        
        for (int skillId : flashSkillIds) {
            if (DataManager.SKILL_DATA.getSkillTemplate(skillId) != null) {
                PacketSendUtility.sendMessage(player, "Testing skill ID: " + skillId);
                
                Skill skill = SkillEngine.getInstance().getSkillFor(player, skillId, 1);
                if (skill != null) {
                    skill.useSkill();
                    PacketSendUtility.sendMessage(player, "Skill executed. Check your position!");
                    break; // Only test one skill to avoid spam
                } else {
                    PacketSendUtility.sendMessage(player, "Could not create skill " + skillId);
                }
            }
        }
        
        PacketSendUtility.sendMessage(player, "Position after: " + player.getX() + ", " + player.getY() + ", " + player.getZ());
        PacketSendUtility.sendMessage(player, "If you're not underground, the position validation is working!");
    }

    private void testPullSkills(Player player) {
        PacketSendUtility.sendMessage(player, "=== Testing Pull Skills ===");
        
        if (player.getTarget() == null) {
            PacketSendUtility.sendMessage(player, "You need to target someone to test pull skills!");
            return;
        }
        
        // Common pull skill IDs (these may vary by server)
        int[] pullSkillIds = {
            417,  // Vacuum Pull
            418,  // Vacuum Pull II
            1234, // Chain Pull (example)
        };
        
        PacketSendUtility.sendMessage(player, "Testing pull skills on target: " + player.getTarget().getName());
        PacketSendUtility.sendMessage(player, "Target position before: " + 
            player.getTarget().getX() + ", " + player.getTarget().getY() + ", " + player.getTarget().getZ());
        
        for (int skillId : pullSkillIds) {
            if (DataManager.SKILL_DATA.getSkillTemplate(skillId) != null) {
                PacketSendUtility.sendMessage(player, "Testing pull skill ID: " + skillId);
                
                Skill skill = SkillEngine.getInstance().getSkillFor(player, skillId, 1);
                if (skill != null) {
                    skill.useSkill();
                    PacketSendUtility.sendMessage(player, "Pull skill executed. Check target position!");
                    break; // Only test one skill to avoid spam
                } else {
                    PacketSendUtility.sendMessage(player, "Could not create skill " + skillId);
                }
            }
        }
        
        if (player.getTarget() != null) {
            PacketSendUtility.sendMessage(player, "Target position after: " + 
                player.getTarget().getX() + ", " + player.getTarget().getY() + ", " + player.getTarget().getZ());
            PacketSendUtility.sendMessage(player, "If target is not underground, the position validation is working!");
        }
    }

    private void testCustomSkill(Player player, String skillIdStr) {
        try {
            int skillId = Integer.parseInt(skillIdStr);
            
            PacketSendUtility.sendMessage(player, "=== Testing Custom Skill " + skillId + " ===");
            
            if (DataManager.SKILL_DATA.getSkillTemplate(skillId) == null) {
                PacketSendUtility.sendMessage(player, "Skill " + skillId + " does not exist!");
                return;
            }
            
            PacketSendUtility.sendMessage(player, "Position before: " + player.getX() + ", " + player.getY() + ", " + player.getZ());
            
            Skill skill = SkillEngine.getInstance().getSkillFor(player, skillId, 1);
            if (skill != null) {
                skill.useSkill();
                PacketSendUtility.sendMessage(player, "Skill " + skillId + " executed successfully!");
                PacketSendUtility.sendMessage(player, "Position after: " + player.getX() + ", " + player.getY() + ", " + player.getZ());
                PacketSendUtility.sendMessage(player, "Check if position validation prevented any issues.");
            } else {
                PacketSendUtility.sendMessage(player, "Could not create skill " + skillId + ". Check if you meet the requirements.");
            }
            
        } catch (NumberFormatException e) {
            PacketSendUtility.sendMessage(player, "Invalid skill ID. Please use a numeric value.");
        }
    }

    private void showHelp(Player player) {
        PacketSendUtility.sendMessage(player, "=== Skill Effects Test Commands ===");
        PacketSendUtility.sendMessage(player, "//testskill flash - Test flash/teleport skills");
        PacketSendUtility.sendMessage(player, "//testskill pull - Test pull effects (requires target)");
        PacketSendUtility.sendMessage(player, "//testskill custom <skillId> - Test specific skill by ID");
        PacketSendUtility.sendMessage(player, "//testskill help - Show this help message");
        PacketSendUtility.sendMessage(player, "");
        PacketSendUtility.sendMessage(player, "These commands help test movement-based skill effects");
        PacketSendUtility.sendMessage(player, "to verify that position validation prevents falling through terrain.");
        PacketSendUtility.sendMessage(player, "");
        PacketSendUtility.sendMessage(player, "Note: Some skills may require specific conditions or targets to work.");
    }

    @Override
    public void onFail(Player player, String message) {
        PacketSendUtility.sendMessage(player, "Command failed: " + message);
    }
}
