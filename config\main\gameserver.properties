#
# ----------------------------
# GS config's:
# ----------------------------

# Server country code (the client checks it against its cc start parameter)
# 1=NA, 2=EU, 7=RU, 99=Region free (allows any client, but client will limit character names to 10 characters)
# Default: 99
gameserver.country.code = 99

# Max level for players
# Actual : 65 (4.X)
gameserver.players.max.level = 65

# Time zone of this GameServer. Make sure to use Continent/City pattern over GMT±X, so daylight saving rules can apply
# See: http://joda-time.sourceforge.net/timezones.html
# Default: empty (system time zone)
gameserver.timezone =

# Enable connection with CS (ChatServer)
# Default: false
gameserver.chatserver.enable = false

# Min level to write in CS channels
# Default: 10
gameserver.chatserver.min_level = 10

# ============================
# Character creation configs:
# ============================
# Character creation mode (Numeric value between 0-2)
# 0: (Default) User can only create characters inside 1 faction.
# 1: User can create characters of both factions on the same account.
# 2: (Character reservation mode) User is bound to 1 faction, can't create more than 2 characters and can't enter the game world.
gameserver.character.creation.mode = 0

# Maximum number of characters per account (Numeric value between 0-8)
# Default: 5 (8 for premium)
gameserver.character.limit.count = 8

# ----------------------------
# Ratio's configs:
# ----------------------------
# Factions that are authorized to create characters (Only enter numeric value between 0-3)
# 0: (Default - No limitations) Both factions can be chosen
# 1: (Asmodians only) You can only create Asmodians characters
# 2: (Elyos only) You can only create Elyos characters
# 3: (Nothing) You can't create any new character
gameserver.character.faction.limitation.mode = 0

# Enable ratios limitation (Server will change the previous config's value when ratios
# Default: false
gameserver.ratio.limitation.enable = false

# ----------------------------

# When a faction ratio reach this value, it's not possible to create new characters of this faction
# Default: 60 (= 60% of the players are of the same faction)
gameserver.ratio.min.value = 60

# Minimum character's level to be taken into account in ratio calculation
# Default: 10
gameserver.ratio.min.required.level = 10

# Minimum characters count in each faction before limitating new characters creation (if enabled)
# Default: 50
gameserver.ratio.min.characters_count = 50

# Maximum character count (sum of both factions) after which ration limitation won't be used anymore
# Default: 500 (If there is more than 500 chars, no need to limit creation anymore ...)
gameserver.ratio.high_player_count.disabling = 500

# ============================
# Misc Configs :
# ============================
# Characters re-entry time in seconds
# Default: 20
gameserver.character.reentry.time = 10

# Minimum time in milliseconds between two skill casts. The game client will enforce wait times accordingly.
# Default: 350
gameserver.min_skill_cast_interval_millis = 350

# Override item wrap limits (pack_count)
# 0 = use default limits from templates
# 1-127 = fixed limit (all non-tradeable, non-soulbound items become wrappable)
# 255 = all non-tradeable, non-soulbound items become infinitely wrappable
# any other positive or negative number = disable item wrapping
# Default: 0
gameserver.item_wrap_limit = 0

# Enable web reward support (players receive items that are inserted into the web_reward DB table)
# Default: false
gameserver.web_rewards.enable = false

# Analyze quest handlers on startup to identify potential bugs
gameserver.analysis.quest_handlers = true

# Location of quest *.java handlers
gameserver.quest.handler_directory = ./data/handlers/quest