#
# ----------------------------
# Group config's:
# ----------------------------

# Time in seconds after a group member is removed after disconnect
# Default: 600 (10 minutes)
gameserver.playergroup.removetime = 600

# Maximum distance between killed monster and party member to receive XP
# Default: 100
gameserver.playergroup.maxdistance = 100

# Enable inviting to group player from other faction
# Default: false
gameserver.group.inviteotherfaction = false

# ----------------------------
# Alliance config's:
# ----------------------------
# Time in seconds after a alliance member is removed after disconnect
# Default: 600 (10 minutes)
gameserver.playeralliance.removetime = 600

# Enable inviting to alliance player from other faction
# Default: false
gameserver.playeralliance.inviteotherfaction = false

# Allow applying for or registering instance groups in the Find Group window even if you're not at the instance entrance (like in version 6.2+)
# Default: false
gameserver.instance_group.form_anywhere = false