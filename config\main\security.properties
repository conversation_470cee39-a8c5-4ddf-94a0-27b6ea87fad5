#
# ----------------------------
# Security config's:
# ----------------------------

# Anti Hack Protection

# AION.bin size check
gameserver.security.aion.bin.check = false

# Enable\disable validation for illegal teleportation
gameserver.security.antihack.teleportation = false

# Enable\disable validation for speed increase client inject
gameserver.security.antihack.speedhack = false

# Speedhack detect counter
gameserver.security.antihack.speedhack.counter = 5

# Enable\disable validation for ignoring abnormals hack
gameserver.security.antihack.abnormal = false

# Abnormal hack alarm counter
# 0 will not allow hackers to move at all, but will logg some unguilty players (don't use this with punish = 3!)
gameserver.security.antihack.abnormal.counter = 1

# Punishment
# 0 - add log record
# 1 - log and put cheater back on last known position
# 2 - log and put cheater back on last known position (after 'speedhack.counter * 3' kick player from game)
# 3 - log and kick player from game
gameserver.security.antihack.punish = 0

# Check for no-animation hacks (prevents premature skill executions and logs suspicious players to audit log)
# Default: true
gameserver.security.check_animations = true

# Kick players who don't pass the ping check (possible speedhack)
# Default: true
gameserver.security.pingcheck.kick = true

# ----------------------------
# CAPTCHA config's:
# ----------------------------
# Enable CAPTCHA service
# Default: false
gameserver.security.captcha.enable = false

# Set CAPTCHA appearance
# OD = when OD
# ALL = when all gathering
# Default: OD
gameserver.security.captcha.appear = OD

# Set CAPTCHA appearance rate
# Default: 5 (%)
gameserver.security.captcha.appear.rate = 5

# Set extraction ban time in seconds
# Default: 3000 (50 min)
gameserver.security.captcha.extraction.ban.time = 3000

# Set extraction ban add time in seconds
# Default: 600 (10 min)
gameserver.security.captcha.extraction.ban.add.time = 600

# Set Bonus flypoint time
# Default: 5
gameserver.security.captcha.bonus.fp.time = 5

# ----------------------------
# PassKey config's:
# ----------------------------
# Enable character passkey
# Default: false
gameserver.security.passkey.enable = false

# Enter the maximum number of incorrect password set
# Default: 5
gameserver.security.passkey.wrong.maxcount = 5

# ----------------------------
# Flood config's:
# ----------------------------
# Flood Message
# Message min interval
# Default: 1 sec
gameserver.security.flood.delay = 1

# Ban after flooding messages
# Default: 6
gameserver.security.flood.msg = 6

# ----------------------------
# Others config's:
# ----------------------------

# Enable fly teleport validator
# Default: false
gameserver.security.validation.flypath = false

# Surveys validation time (test)
# Default: 20
gameserver.security.survey.delay.minute = 20

# Restriction mode for multi-clienting:
# NONE - Players are allowed to log in multiple accounts per computer
# FULL - Players are allowed to log in one account per computer
# SAME_FACTION - Players are allowed to log in multiple accounts per computer, but only log in characters of the same faction
gameserver.security.multi_clienting.restriction_mode = NONE

# Comma separated list of MAC addresses that are allowed to log in regardless of the configured restrictions.
gameserver.security.multi_clienting.ignored_mac_addresses =

# If multi-clienting is restricted to the same faction, logging in characters of one faction will be denied until all characters of the opposite
# faction have been offline for the specified amount of time.
gameserver.security.multi_clienting.faction_switch_cooldown_minutes = 20

# Enable login checks for accounts that are locked to a SSD or HDD serial number.
# Locked accounts can only be logged in from the game client on a drive with the locked serial number.
# Accounts can be manually locked and unlocked via the `.lock` player command.
# Default: false
gameserver.security.hdd_serial_lock.enable = false

# Automatically lock unlocked accounts on login to the client's SSD or HDD serial number
# Default: false
gameserver.security.hdd_serial_lock.auto_lock = false
