package com.aionemu.gameserver.services;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.aionemu.gameserver.configs.main.GeoDataConfig;
import com.aionemu.gameserver.geoEngine.math.Vector3f;
import com.aionemu.gameserver.model.gameobjects.Creature;
import com.aionemu.gameserver.model.gameobjects.VisibleObject;
import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.world.geo.GeoService;

/**
 * Enhanced position validation service for skill effects and teleportation.
 * Provides comprehensive ground checking and safe position validation to prevent
 * players from falling through terrain or being placed at invalid positions.
 * 
 * <AUTHOR> by AI Assistant
 */
public class PositionValidationService {

    private static final Logger log = LoggerFactory.getLogger(PositionValidationService.class);
    private static final PositionValidationService instance = new PositionValidationService();

    public static PositionValidationService getInstance() {
        return instance;
    }

    /**
     * Validates and corrects a position to ensure it's safe for a player to be placed at.
     * This method performs comprehensive ground checking and collision detection.
     * 
     * @param creature The creature to validate position for
     * @param targetX Target X coordinate
     * @param targetY Target Y coordinate
     * @param targetZ Target Z coordinate
     * @param originalX Original X coordinate (fallback)
     * @param originalY Original Y coordinate (fallback)
     * @param originalZ Original Z coordinate (fallback)
     * @return A safe Vector3f position, or null if no safe position could be found
     */
    public Vector3f validateAndCorrectPosition(Creature creature, float targetX, float targetY, float targetZ,
                                             float originalX, float originalY, float originalZ) {
        if (!GeoDataConfig.POSITION_VALIDATION_ENABLE) {
            return new Vector3f(targetX, targetY, targetZ);
        }

        try {
            // First, try to find ground at the target position
            Vector3f validatedPosition = findSafeGroundPosition(creature, targetX, targetY, targetZ);
            
            if (validatedPosition != null) {
                if (GeoDataConfig.POSITION_VALIDATION_DEBUG_LOGGING) {
                    log.debug("Position validation successful for {} at ({}, {}, {}) -> ({}, {}, {})",
                        creature.getName(), targetX, targetY, targetZ,
                        validatedPosition.getX(), validatedPosition.getY(), validatedPosition.getZ());
                }
                return validatedPosition;
            }

            // If no safe position found at target, try fallback to original position
            if (GeoDataConfig.POSITION_VALIDATION_FALLBACK_ENABLED) {
                validatedPosition = findSafeGroundPosition(creature, originalX, originalY, originalZ);
                
                if (validatedPosition != null) {
                    if (GeoDataConfig.POSITION_VALIDATION_DEBUG_LOGGING) {
                        log.debug("Position validation fallback successful for {} at ({}, {}, {})",
                            creature.getName(), originalX, originalY, originalZ);
                    }
                    return validatedPosition;
                }
            }

            // Last resort: return original position
            log.warn("Position validation failed for {} at ({}, {}, {}), using original position",
                creature.getName(), targetX, targetY, targetZ);
            return new Vector3f(originalX, originalY, originalZ);

        } catch (Exception e) {
            log.error("Error during position validation for {}: {}", creature.getName(), e.getMessage(), e);
            return new Vector3f(originalX, originalY, originalZ);
        }
    }

    /**
     * Finds a safe ground position at the specified coordinates.
     * 
     * @param creature The creature to find position for
     * @param x X coordinate
     * @param y Y coordinate
     * @param z Z coordinate
     * @return A safe Vector3f position, or null if no safe position found
     */
    private Vector3f findSafeGroundPosition(Creature creature, float x, float y, float z) {
        int worldId = creature.getWorldId();
        int instanceId = creature.getInstanceId();
        
        // Calculate search range
        float maxZRange = GeoDataConfig.POSITION_VALIDATION_MAX_Z_RANGE;
        float zMax = z + maxZRange;
        float zMin = z - maxZRange;
        
        // Try to find ground using GeoService
        float groundZ = GeoService.getInstance().getZ(worldId, x, y, zMax, zMin, instanceId);
        
        if (!Float.isNaN(groundZ)) {
            // Validate that the ground position is safe
            if (isPositionSafe(creature, x, y, groundZ)) {
                return new Vector3f(x, y, groundZ);
            }
        }
        
        // If direct ground search failed, try collision-based approach
        Vector3f collisionPosition = GeoService.getInstance().getClosestCollision(creature, x, y, z);
        
        if (collisionPosition != null && isPositionSafe(creature, collisionPosition.getX(), 
                                                       collisionPosition.getY(), collisionPosition.getZ())) {
            return collisionPosition;
        }
        
        return null;
    }

    /**
     * Checks if a position is safe for a creature to be placed at.
     * 
     * @param creature The creature
     * @param x X coordinate
     * @param y Y coordinate
     * @param z Z coordinate
     * @return true if position is safe, false otherwise
     */
    private boolean isPositionSafe(Creature creature, float x, float y, float z) {
        try {
            int worldId = creature.getWorldId();
            int instanceId = creature.getInstanceId();
            
            // Check if there's solid ground beneath the position
            float safeDistance = GeoDataConfig.POSITION_VALIDATION_SAFE_DISTANCE;
            float groundZ = GeoService.getInstance().getZ(worldId, x, y, z + 1, z - safeDistance * 2, instanceId);
            
            if (Float.isNaN(groundZ)) {
                return false; // No ground found
            }
            
            // Check if the Z difference is reasonable (not too far from expected ground)
            if (Math.abs(z - groundZ) > safeDistance * 2) {
                return false; // Position is too far from actual ground
            }
            
            // Additional safety check: ensure no collision directly above the position
            // This prevents placing players inside objects or under low ceilings
            if (GeoService.getInstance().canSee(worldId, x, y, z, x, y, z + 2, instanceId, null)) {
                return true;
            }
            
            return false; // There's an obstacle above the position
            
        } catch (Exception e) {
            log.warn("Error checking position safety for {}: {}", creature.getName(), e.getMessage());
            return false;
        }
    }

    /**
     * Validates a position for skill effects specifically.
     * This method is optimized for skill effect validation and includes additional checks.
     * 
     * @param creature The creature using the skill
     * @param targetX Target X coordinate
     * @param targetY Target Y coordinate
     * @param targetZ Target Z coordinate
     * @return A safe Vector3f position
     */
    public Vector3f validateSkillEffectPosition(Creature creature, float targetX, float targetY, float targetZ) {
        return validateAndCorrectPosition(creature, targetX, targetY, targetZ,
                                        creature.getX(), creature.getY(), creature.getZ());
    }

    /**
     * Validates a position for teleportation specifically.
     * This method includes additional checks for teleportation safety.
     * 
     * @param player The player being teleported
     * @param targetX Target X coordinate
     * @param targetY Target Y coordinate
     * @param targetZ Target Z coordinate
     * @return A safe Vector3f position
     */
    public Vector3f validateTeleportPosition(Player player, float targetX, float targetY, float targetZ) {
        Vector3f validatedPosition = validateAndCorrectPosition(player, targetX, targetY, targetZ,
                                                              player.getX(), player.getY(), player.getZ());
        
        // Additional teleportation-specific validation
        if (validatedPosition != null && player.isFlying()) {
            // For flying players, ensure there's enough clearance above
            float clearanceZ = validatedPosition.getZ() + 5.0f;
            if (!GeoService.getInstance().canSee(player.getWorldId(), 
                                               validatedPosition.getX(), validatedPosition.getY(), validatedPosition.getZ(),
                                               validatedPosition.getX(), validatedPosition.getY(), clearanceZ,
                                               player.getInstanceId(), null)) {
                // Not enough clearance, try to find a better position
                float groundZ = GeoService.getInstance().getZ(player.getWorldId(), 
                                                            validatedPosition.getX(), validatedPosition.getY(), 
                                                            clearanceZ, validatedPosition.getZ() - 2,
                                                            player.getInstanceId());
                if (!Float.isNaN(groundZ)) {
                    validatedPosition.setZ(groundZ);
                }
            }
        }
        
        return validatedPosition;
    }

    /**
     * Quick validation check for positions that don't require full correction.
     * 
     * @param creature The creature
     * @param x X coordinate
     * @param y Y coordinate
     * @param z Z coordinate
     * @return true if position appears safe, false otherwise
     */
    public boolean isPositionValid(Creature creature, float x, float y, float z) {
        if (!GeoDataConfig.POSITION_VALIDATION_ENABLE) {
            return true;
        }
        
        return isPositionSafe(creature, x, y, z);
    }
}
