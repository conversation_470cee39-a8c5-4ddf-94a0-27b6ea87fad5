#
# ----------------------------
# Admin config's:
# ----------------------------

# Custom name tag for each access level, where %s is the placeholder for the players name.
# Specify a comma separated list, starting with the tag for access level 1, or use "" to deactivate tags completely.
# Default:
# Access level 1 = [empty]
# Access level 2 = Junior Dev
# Access level 3 = Dev
# Access level 4 = Junior Event Master
# Access level 5 = Event Master
# Access level 6 = Junior Game Master
# Access level 7 = Game Master
# Access level 8 = Senior Game Master
# Access level 9 = Admin
gameserver.administration.customtags = %s, \u00BBJDev\u00AB\uE04A%s, \u00BBDev\u00AB\uE04A%s, \u00BBJEM\u00AB\uE04A%s, \u00BBEM\u00AB\uE04A%s, \u00BBJGM\u00AB\uE04A%s, \u00BBGM\u00AB\uE04A%s, \u00BBSGM\u00AB\uE04A%s, \u00BBAdmin\u00AB\uE04A%s

# ----------------------------
# Access config:
# ----------------------------
# If the player has the given access level or higher, the option is active for him

# Unrestricted item trades (restricted staff members can only trade items listed in item.restriction.txt to normal players)
# Default: 1
gameserver.administration.unrestricted_itemtrade = 1

# GM Panel
# Default: 2
gameserver.administration.gm_panel = 2

# GM skills
# Default: 8
gameserver.administration.gm_skills = 8

# Free Flight Everywhere
# Default: 1
gameserver.administration.flight.free_fly = 1

# Unlimited Flight Time for GM's
# Default: 1
gameserver.administration.flight.unlimited_time = 1

# Resurrect anywhere without skill
# Default: 1
gameserver.administration.auto_res = 1

# Can view player details, even when he denies it
# Default: 5
gameserver.administration.view_player_details = 5

# No instance requirements
# Default: 2
gameserver.administration.instance.enter_all = 2

# Open every door without using a key
# Default: 6
gameserver.administration.instance.open_doors = 6

# Show instance door info when clicking them
# Default: 9
gameserver.administration.instance.door_info = 9

# Enter closed houses when clicking the door
# Default: 9
gameserver.administration.house.enter_all = 9

# Show house address when clicking the door
# Default: 9
gameserver.administration.house.show_address = 9

# Show additional information when clicking through dialog windows (quest ID, dialog action, ...)
# Default: 9
gameserver.administration.dialog_info = 9

# Show success state/chance information when enchanting items
# Default: 9
gameserver.administration.enchant_info = 9

# Show zone names when client notifies about passing one
# Default: 9
gameserver.administration.zone_info = 9

# Receive messages about possibly illegal player actions from AuditLogger
# Default: 9
gameserver.administration.audit_info = 9

# //quest command:
# Minimum access level to use parameters other than <reset> and <start>
# Default: 9
gameserver.administration.command.quest.advanced_parameters = 9

# ----------------------------
# Connection options:
# ----------------------------
# The following options are active for all staff members (access level > 0)

# Comma separated list of chat commands to execute on login
# Default: //invis, //invul, //enemy none, //see
gameserver.administration.login.execute_commands = //see

# Minimum access level to see the server build date and revision on login (custom messages can be defined in events.xml via <login_message/>)
# Default: 9
gameserver.administration.login.print_revision = 9

# Login announce levels
# 1,2,3 etc - announce only staff members with that access level ("" = off)
# Default: * (announce all of them)
gameserver.administration.login.announce_levels = *

# GM login announce mode (false = announce only to other GMs with the specified levels)
# Default: true (announce to all players)
gameserver.administration.login.announce_to_all_players = true

# GM logout announce mode (false = announce only to other GMs with the specified levels)
# Default: true (announce to all players)
gameserver.administration.logout.announce_to_all_players = true
