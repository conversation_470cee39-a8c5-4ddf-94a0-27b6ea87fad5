#
# ----------------------------
# Thread config's:
# ----------------------------

# Number of threads that are dedicated to handle instant tasks.
# A value of 0 means auto. On auto, the number is usually equal to the number of logical CPU cores as reported by Runtime#availableProcessors(), but
# never less than four.
# Default: 0
gameserver.thread.base_pool_size = 0

# Number of threads that are dedicated to handle scheduled tasks.
# A value of 0 means auto. On auto, the number is usually equal to the number of logical CPU cores as reported by Runtime#availableProcessors(), but
# never less than four.
# Default: 0
gameserver.thread.scheduled_pool_size = 0

# Logs a warning if any thread needs longer than this time to finish a task. Time in milliseconds.
# Default: 5000
gameserver.thread.runtime = 5000

# Use higher thread priorities for instant thread pool
# Note: in Linux you must be root and use switches: -XX:+UseThreadPriorities -XX:ThreadPriorityPolicy=1
# Default: false
gameserver.thread.usepriority = false