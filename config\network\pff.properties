#
# ----------------------------
# Packet flood filter configs:
# ----------------------------

# Packet flood handling
# 0 - off
# 1 - log message
# 2 - log message and disconnect client
# Default: 1
gameserver.network.pff.mode = 1

# Below you can list packet opcodes followed by a threshold in milliseconds. If a packet comes in faster than the threshold, action is taken depending
# on the above configured mode.
# Schema is "gameserver.network.pff.packet.<opcode> = <msec>"
# Example: gameserver.network.pff.packet.0x0C4 = 150

# CM_CASTSPELL
# gameserver.network.pff.packet.0x0C4 = 150