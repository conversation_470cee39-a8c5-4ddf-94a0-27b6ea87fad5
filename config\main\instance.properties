#
# ----------------------------
# Instance configs:
# ----------------------------

# It's how many times the cooldown will be shorter
# Example: 3 = 1/3 of retail cooldown, 0 - disable cooldown
# Default: 1
gameserver.instance.cooldown_rate = 1

# List of instance map IDs, which should not be affected by the customized cooldown rate
# Default: empty
# Example: 300080000,300090000,300060000
gameserver.instance.cooldown_rate.excluded_maps =

# Time in seconds until an empty instance will be destroyed
# Default: 600 (10 minutes)
gameserver.instance.destroy_delay_seconds = 600

# Time in seconds until an empty solo instance will be destroyed
# Default: 600 (10 minutes)
gameserver.instance.solo.destroy_delay_seconds = 600

# Duel in Instance
# Default: true
gameserver.instance.duel.enable = true

# Location of instance *.java handlers
gameserver.instance.handler_directory = ./data/handlers/instance