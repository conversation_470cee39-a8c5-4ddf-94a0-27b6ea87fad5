#
# ----------------------------
# Network Config's:
# ----------------------------

# Address where Aion clients will attempt to connect to (format is host:port).
# The host part can be an IPv4 address or a host/domain name which resolves to an IPv4 address since the client does not support IPv6.
# By default, the connect address is the same as the socket address (if the host part is set to 0.0.0.0 the server will auto select the most suitable local
# IPv4 address and log it).
gameserver.network.client.connect_address = ${gameserver.network.client.socket_address}

# Local address where G<PERSON> will listen for Aion client connections (0.0.0.0 = bind any local IP)
gameserver.network.client.socket_address = 0.0.0.0:6666

# Address (host/domain name or IP) of the login server
gameserver.network.login.address = 0.0.0.0:9034

# Id of this game server
gameserver.network.login.gsid = 1

# Password of this game server
gameserver.network.login.password = ThuannyRoman@99

# Minimum required access level for accounts trying to connect (=maintenance mode)
gameserver.network.login.min_accesslevel = 0

# Maximum online players on the server
gameserver.network.login.max_players = 100

# Address (host/domain name or IP) of the chat server
gameserver.network.chat.address = 0.0.0.0:9031

# Password of this game server for chat server
gameserver.network.chat.password = ThuannyRoman@99

# Number of extra threads dedicated only to read/write network data.
# Value < 1 means that acceptor thread will also handle read & write.
# Value > 0 means there will be x dedicated read/write threads + 1 acceptor.
gameserver.network.nio.threads = 1

# Number of threads (min) that will be used to execute client packets
gameserver.network.packet.processor.threads.min = 4

# Number of threads (max) that will be used to execute client packets
gameserver.network.packet.processor.threads.max = 4

# Threshold used to decide when packet processor thread should be killed
# It have effect only if min threads != max threads
gameserver.network.packet.processor.threshold.kill = 3

# Threshold used to decide when extra packet processor thread should be spawned
# It have effect only if min threads != max threads
gameserver.network.packet.processor.threshold.spawn = 50

# Enable/disable log messages for unknown packets received from the aion client
gameserver.network.logging.unknown_packets = false

# Enable/disable log messages for ignored aion client packets (due to invalid connection state)
gameserver.network.logging.ignored_packets = false

# Enable flood protector
gameserver.network.flood.connections = false

# Flood ms in tick
gameserver.network.flood.tick = 1000

# Short period
gameserver.network.flood.short.warn = 10
gameserver.network.flood.short.reject = 20
gameserver.network.flood.short.tick = 10

# Long period
gameserver.network.flood.long.warn = 30
gameserver.network.flood.long.reject = 60
gameserver.network.flood.long.tick = 60