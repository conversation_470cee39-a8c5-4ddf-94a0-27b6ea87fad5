package admincommands;

import com.aionemu.gameserver.model.ChatType;
import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.services.DevilsMarkService;
import com.aionemu.gameserver.utils.PacketSendUtility;
import com.aionemu.gameserver.utils.chathandlers.AdminCommand;
import com.aionemu.gameserver.world.World;

/**
 * Admin command for managing the Devil's Mark system
 *
 * <AUTHOR> Implementation
 */
public class DevilsMark extends AdminCommand {

    public DevilsMark() {
        super("devilsmark", "Manages the Devil's Mark PvP bounty hunting system.");

        // @formatter:off
        setSyntaxInfo(
            "<status> - Show current marked player and time remaining.",
            "<pick> <player> - Mark a specific player with the Devil's Mark.",
            "<unmark> - Remove the current Devil's Mark.",
            "<info> - Show system configuration and status.",
            "<debug> - Show detailed debug information."
        );
        // @formatter:on
    }

    @Override
    protected void execute(Player admin, String... params) {
        if (params.length == 0) {
            sendInfo(admin);
            return;
        }

        String action = params[0].toLowerCase();
        DevilsMarkService service = DevilsMarkService.getInstance();

        switch (action) {
            case "status":
                showStatus(admin, service);
                break;
            case "pick":
                if (params.length < 2) {
                    sendInfo(admin, "Usage: //devilsmark pick <player_name>");
                    return;
                }
                pickPlayer(admin, params[1], service);
                break;
            case "unmark":
                unmarkPlayer(admin, service);
                break;
            case "info":
                showInfo(admin, service);
                break;
            case "debug":
                showDebug(admin, service);
                break;
            default:
                sendInfo(admin);
                break;
        }
    }

    private void showStatus(Player admin, DevilsMarkService service) {
        Player marked = service.getCurrentMarkedPlayer();
        if (marked == null) {
            PacketSendUtility.sendMessage(admin, "No player is currently marked by the Devil.");
        } else {
            long timeRemaining = service.getTimeRemaining();
            int hoursRemaining = (int) (timeRemaining / (1000 * 60 * 60));
            int minutesRemaining = (int) ((timeRemaining % (1000 * 60 * 60)) / (1000 * 60));

            PacketSendUtility.sendMessage(admin,
                String.format("Currently marked: %s (Time remaining: %dh %dm)",
                    marked.getName(), hoursRemaining, minutesRemaining));
        }
    }

    private void pickPlayer(Player admin, String playerName, DevilsMarkService service) {
        // Find the player by name
        Player targetPlayer = World.getInstance().getPlayer(playerName);

        if (targetPlayer == null) {
            PacketSendUtility.sendMessage(admin, "Player '" + playerName + "' not found or not online.");
            return;
        }

        // Check if someone is already marked
        Player currentMarked = service.getCurrentMarkedPlayer();
        if (currentMarked != null) {
            PacketSendUtility.sendMessage(admin,
                "Player '" + currentMarked.getName() + "' is already marked. Use //devilsmark unmark first.");
            return;
        }

        // Try to mark the player
        boolean success = service.manuallyMarkPlayer(targetPlayer);

        if (success) {
            PacketSendUtility.sendMessage(admin,
                "Successfully marked player '" + targetPlayer.getName() + "' with the Devil's Mark!");
            PacketSendUtility.sendMessage(admin,
                "The server has been notified and the hunt begins!");
        } else {
            PacketSendUtility.sendMessage(admin,
                "Failed to mark player '" + targetPlayer.getName() + "'. " +
                "Player may not be eligible (check level, location, or system status).");
        }
    }

    private void unmarkPlayer(Player admin, DevilsMarkService service) {
        Player marked = service.getCurrentMarkedPlayer();
        if (marked == null) {
            PacketSendUtility.sendMessage(admin, "No player is currently marked.");
            return;
        }

        // Try to unmark the player
        boolean success = service.manuallyUnmarkPlayer();

        if (success) {
            PacketSendUtility.sendMessage(admin,
                "Successfully removed the Devil's Mark from '" + marked.getName() + "'.");
            PacketSendUtility.sendMessage(admin,
                "The hunt has ended and the next marking cycle will begin.");
        } else {
            PacketSendUtility.sendMessage(admin,
                "Failed to remove the Devil's Mark. Please try again.");
        }
    }

    private void showInfo(Player admin, DevilsMarkService service) {
        PacketSendUtility.sendMessage(admin, "=== Devil's Mark System Info ===");
        PacketSendUtility.sendMessage(admin, "System enabled: " +
            com.aionemu.gameserver.configs.main.CustomConfig.DEVILS_MARK_ENABLED);
        PacketSendUtility.sendMessage(admin, "Mark duration: " +
            com.aionemu.gameserver.configs.main.CustomConfig.DEVILS_MARK_DURATION_HOURS + " hours");
        PacketSendUtility.sendMessage(admin, "Min level: " +
            com.aionemu.gameserver.configs.main.CustomConfig.DEVILS_MARK_MIN_LEVEL);
        PacketSendUtility.sendMessage(admin, "Killer AP reward: " +
            com.aionemu.gameserver.configs.main.CustomConfig.DEVILS_MARK_KILLER_AP_REWARD);
        PacketSendUtility.sendMessage(admin, "Killer GP reward: " +
            com.aionemu.gameserver.configs.main.CustomConfig.DEVILS_MARK_KILLER_GP_REWARD);
        PacketSendUtility.sendMessage(admin, "Killer items: " +
            com.aionemu.gameserver.configs.main.CustomConfig.DEVILS_MARK_KILLER_ITEMS);
        PacketSendUtility.sendMessage(admin, "Killer counts: " +
            com.aionemu.gameserver.configs.main.CustomConfig.DEVILS_MARK_KILLER_COUNTS);
        PacketSendUtility.sendMessage(admin, "Marked killer AP reward: " +
            com.aionemu.gameserver.configs.main.CustomConfig.DEVILS_MARK_MARKED_KILLER_AP_REWARD);
        PacketSendUtility.sendMessage(admin, "Marked killer GP reward: " +
            com.aionemu.gameserver.configs.main.CustomConfig.DEVILS_MARK_MARKED_KILLER_GP_REWARD);
        PacketSendUtility.sendMessage(admin, "Marked killer items: " +
            com.aionemu.gameserver.configs.main.CustomConfig.DEVILS_MARK_MARKED_KILLER_ITEMS);
        PacketSendUtility.sendMessage(admin, "Marked killer counts: " +
            com.aionemu.gameserver.configs.main.CustomConfig.DEVILS_MARK_MARKED_KILLER_COUNTS);
        PacketSendUtility.sendMessage(admin, "Survival items: " +
            com.aionemu.gameserver.configs.main.CustomConfig.DEVILS_MARK_SURVIVAL_ITEMS);
        PacketSendUtility.sendMessage(admin, "Survival counts: " +
            com.aionemu.gameserver.configs.main.CustomConfig.DEVILS_MARK_SURVIVAL_COUNTS);
        PacketSendUtility.sendMessage(admin, "Visual effects: " +
            com.aionemu.gameserver.configs.main.CustomConfig.DEVILS_MARK_VISUAL_EFFECTS);
        PacketSendUtility.sendMessage(admin, "Healing enabled: " +
            com.aionemu.gameserver.configs.main.CustomConfig.DEVILS_MARK_HEALING_ENABLE);
        PacketSendUtility.sendMessage(admin, "Healing skills: " +
            com.aionemu.gameserver.configs.main.CustomConfig.DEVILS_MARK_HEALING_SKILLS);
        PacketSendUtility.sendMessage(admin, "Healing interval: " +
            com.aionemu.gameserver.configs.main.CustomConfig.DEVILS_MARK_HEALING_INTERVAL + " seconds");
        PacketSendUtility.sendMessage(admin, "Remove safe zone protection: " +
            com.aionemu.gameserver.configs.main.CustomConfig.DEVILS_MARK_REMOVE_SAFE_ZONE_PROTECTION);
        PacketSendUtility.sendMessage(admin, "Cross-faction targeting: " +
            com.aionemu.gameserver.configs.main.CustomConfig.DEVILS_MARK_CROSS_FACTION_TARGETING);

        showStatus(admin, service);
    }

    private void showDebug(Player admin, DevilsMarkService service) {
        String debugInfo = service.getDebugStatus();
        String[] lines = debugInfo.split("\n");
        for (String line : lines) {
            PacketSendUtility.sendMessage(admin, line);
        }
    }
}
