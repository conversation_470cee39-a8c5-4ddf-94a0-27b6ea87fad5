package data.handlers.admincommands;

import com.aionemu.gameserver.geoEngine.math.Vector3f;
import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.services.PositionValidationService;
import com.aionemu.gameserver.services.teleport.TeleportService;
import com.aionemu.gameserver.utils.PacketSendUtility;
import com.aionemu.gameserver.utils.chathandlers.AdminCommand;
import com.aionemu.gameserver.world.geo.GeoService;

/**
 * Admin command for testing position validation functionality.
 * This command helps verify that the enhanced geo data system properly
 * validates positions and prevents players from falling through terrain.
 * 
 * Usage:
 * //testpos validate - Test position validation at current location
 * //testpos teleport <x> <y> <z> - Test teleportation with position validation
 * //testpos ground - Test ground detection at current location
 * //testpos unsafe <x> <y> <z> - Test teleportation to potentially unsafe position
 * 
 * <AUTHOR> by AI Assistant
 */
public class TestPositionValidation extends AdminCommand {

    public TestPositionValidation() {
        super("testpos");
    }

    @Override
    public void execute(Player player, String... params) {
        if (params.length == 0) {
            showHelp(player);
            return;
        }

        switch (params[0].toLowerCase()) {
            case "validate":
                testPositionValidation(player);
                break;
            case "teleport":
                if (params.length >= 4) {
                    testTeleportValidation(player, params[1], params[2], params[3]);
                } else {
                    PacketSendUtility.sendMessage(player, "Usage: //testpos teleport <x> <y> <z>");
                }
                break;
            case "ground":
                testGroundDetection(player);
                break;
            case "unsafe":
                if (params.length >= 4) {
                    testUnsafeTeleport(player, params[1], params[2], params[3]);
                } else {
                    PacketSendUtility.sendMessage(player, "Usage: //testpos unsafe <x> <y> <z>");
                }
                break;
            case "help":
            default:
                showHelp(player);
                break;
        }
    }

    private void testPositionValidation(Player player) {
        PacketSendUtility.sendMessage(player, "=== Position Validation Test ===");
        
        float x = player.getX();
        float y = player.getY();
        float z = player.getZ();
        
        PacketSendUtility.sendMessage(player, "Current position: " + x + ", " + y + ", " + z);
        
        // Test if current position is valid
        boolean isValid = PositionValidationService.getInstance().isPositionValid(player, x, y, z);
        PacketSendUtility.sendMessage(player, "Position is valid: " + isValid);
        
        // Test position validation with correction
        Vector3f validatedPos = PositionValidationService.getInstance().validateTeleportPosition(player, x, y, z);
        PacketSendUtility.sendMessage(player, "Validated position: " + 
            validatedPos.getX() + ", " + validatedPos.getY() + ", " + validatedPos.getZ());
        
        // Check if position was corrected
        if (Math.abs(x - validatedPos.getX()) > 0.1f || 
            Math.abs(y - validatedPos.getY()) > 0.1f || 
            Math.abs(z - validatedPos.getZ()) > 0.1f) {
            PacketSendUtility.sendMessage(player, "Position was corrected by validation system!");
        } else {
            PacketSendUtility.sendMessage(player, "Position validation passed without correction.");
        }
    }

    private void testTeleportValidation(Player player, String xStr, String yStr, String zStr) {
        try {
            float x = Float.parseFloat(xStr);
            float y = Float.parseFloat(yStr);
            float z = Float.parseFloat(zStr);
            
            PacketSendUtility.sendMessage(player, "=== Teleport Validation Test ===");
            PacketSendUtility.sendMessage(player, "Target position: " + x + ", " + y + ", " + z);
            
            // Test position validation before teleporting
            Vector3f validatedPos = PositionValidationService.getInstance().validateTeleportPosition(player, x, y, z);
            PacketSendUtility.sendMessage(player, "Validated position: " + 
                validatedPos.getX() + ", " + validatedPos.getY() + ", " + validatedPos.getZ());
            
            // Teleport using the enhanced TeleportService (which includes validation)
            TeleportService.teleportTo(player, player.getWorldId(), x, y, z);
            PacketSendUtility.sendMessage(player, "Teleportation completed with position validation.");
            
        } catch (NumberFormatException e) {
            PacketSendUtility.sendMessage(player, "Invalid coordinates. Please use numeric values.");
        }
    }

    private void testGroundDetection(Player player) {
        PacketSendUtility.sendMessage(player, "=== Ground Detection Test ===");
        
        float x = player.getX();
        float y = player.getY();
        float z = player.getZ();
        
        // Test ground detection at various Z levels
        float[] testZLevels = {z + 10, z + 5, z, z - 5, z - 10};
        
        for (float testZ : testZLevels) {
            float groundZ = GeoService.getInstance().getZ(player.getWorldId(), x, y, testZ + 2, testZ - 2, player.getInstanceId());
            
            if (!Float.isNaN(groundZ)) {
                PacketSendUtility.sendMessage(player, "Ground found at Z=" + testZ + " -> Ground Z=" + groundZ);
            } else {
                PacketSendUtility.sendMessage(player, "No ground found at Z=" + testZ);
            }
        }
        
        // Test collision detection
        Vector3f collision = GeoService.getInstance().getClosestCollision(player, x, y, z);
        if (collision != null) {
            PacketSendUtility.sendMessage(player, "Closest collision: " + 
                collision.getX() + ", " + collision.getY() + ", " + collision.getZ());
        } else {
            PacketSendUtility.sendMessage(player, "No collision detected.");
        }
    }

    private void testUnsafeTeleport(Player player, String xStr, String yStr, String zStr) {
        try {
            float x = Float.parseFloat(xStr);
            float y = Float.parseFloat(yStr);
            float z = Float.parseFloat(zStr);
            
            PacketSendUtility.sendMessage(player, "=== Unsafe Teleport Test ===");
            PacketSendUtility.sendMessage(player, "WARNING: This will attempt to teleport to a potentially unsafe position!");
            PacketSendUtility.sendMessage(player, "Target position: " + x + ", " + y + ", " + z);
            
            // Store original position for fallback
            float origX = player.getX();
            float origY = player.getY();
            float origZ = player.getZ();
            
            // Test what the validation system would do
            Vector3f validatedPos = PositionValidationService.getInstance().validateTeleportPosition(player, x, y, z);
            PacketSendUtility.sendMessage(player, "Validation system suggests: " + 
                validatedPos.getX() + ", " + validatedPos.getY() + ", " + validatedPos.getZ());
            
            // Perform the teleport (the TeleportService will apply validation automatically)
            TeleportService.teleportTo(player, player.getWorldId(), x, y, z);
            
            PacketSendUtility.sendMessage(player, "Teleport completed. Check if you're at a safe position!");
            PacketSendUtility.sendMessage(player, "Original position was: " + origX + ", " + origY + ", " + origZ);
            
        } catch (NumberFormatException e) {
            PacketSendUtility.sendMessage(player, "Invalid coordinates. Please use numeric values.");
        }
    }

    private void showHelp(Player player) {
        PacketSendUtility.sendMessage(player, "=== Position Validation Test Commands ===");
        PacketSendUtility.sendMessage(player, "//testpos validate - Test position validation at current location");
        PacketSendUtility.sendMessage(player, "//testpos teleport <x> <y> <z> - Test teleportation with validation");
        PacketSendUtility.sendMessage(player, "//testpos ground - Test ground detection at current location");
        PacketSendUtility.sendMessage(player, "//testpos unsafe <x> <y> <z> - Test teleport to unsafe position");
        PacketSendUtility.sendMessage(player, "//testpos help - Show this help message");
        PacketSendUtility.sendMessage(player, "");
        PacketSendUtility.sendMessage(player, "These commands help test the enhanced geo data system");
        PacketSendUtility.sendMessage(player, "and verify that position validation prevents falling through terrain.");
    }

    @Override
    public void onFail(Player player, String message) {
        PacketSendUtility.sendMessage(player, "Command failed: " + message);
    }
}
