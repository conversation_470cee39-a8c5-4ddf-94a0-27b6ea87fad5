#
# ----------------------------
# Database Config's:
# ----------------------------

# This is the database url
# Set the timezone in gameserver.properties if you get an error during startup. It is needed to avoid timestamp errors during daylight savings clock change.
database.url = **************************************************=${gameserver.timezone}&characterEncoding=UTF-8

# Database user
database.user = root

# Database password
database.password = ThuannyRoman@99

# Maximal amount of connections kept in connection pool
database.connectionpool.connections.max = 5

# Maximum wait time when getting a DB connection, before throwing a timeout error
database.connectionpool.timeout = 5000