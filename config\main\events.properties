#
# ----------------------------
# Event config's:
# ----------------------------
# Disabled Events (insert comma separated names from timed_events or * for all)
# Start and end times for events are set in timed_events (no start and end time means permanently active)
# Default: empty string (none)
gameserver.event.service.disabled_events = 

# ----------------------------
# Event Upgrade Arcade:
# ----------------------------
# Enable Upgrade Arcade
# Default: false
gameserver.event.arcade.enable = false

# Upgrade Arcade tokens needed to resume
# Default: 3
gameserver.event.arcade.resume_token = 3

# ----------------------------
# Invasion / World Raids:
# ----------------------------
# Enable World Raids - Default: true
gameserver.worldraid.enable = true

# Enable World Raid spawn message - Default: true
gameserver.worldraid.use_spawn_msg = true

# ----------------------------
# Headhunting:
# ----------------------------
# Enable Headhunting
# Default: false
gameserver.event.headhunting.enable = false

# Headhunting allowed maps (f.e. 600090000,600100000)
# Default: 
gameserver.event.headhunting.maps = 

# Headhunting needed kills to receive the consolation prize
# Default: 50
gameserver.event.headhunting.consolation_prize_kills = 50