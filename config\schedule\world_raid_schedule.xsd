<?xml version="1.0" standalone="yes"?>
<xs:schema version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:element name="world_raid_schedule" type="WorldRaidSchedule" />
	<xs:element name="world_raid_schedules" type="WorldRaidSchedules" />

	<xs:complexType name="WorldRaidSchedules">
		<xs:sequence>
			<xs:element ref="world_raid_schedule" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="WorldRaidSchedule">
		<xs:sequence>
			<xs:element name="start_time" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
		<xs:attribute name="id" type="xs:string" use="required" />
		<xs:attribute name="locations" type="IntList" use="required" />
		<xs:attribute name="min_count" type="xs:int" default="0" />
		<xs:attribute name="max_count" type="xs:int" default="0" />
		<xs:attribute name="is_special_raid" type="xs:boolean" />
	</xs:complexType>
	<xs:simpleType name="IntList">
		<xs:list itemType="xs:int" />
	</xs:simpleType>
</xs:schema>