package ai.portals;

import com.aionemu.gameserver.ai.AIName;
import com.aionemu.gameserver.configs.main.CustomConfig;
import com.aionemu.gameserver.model.ChatType;
import com.aionemu.gameserver.model.gameobjects.Npc;
import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.services.OneVsOneService;
import com.aionemu.gameserver.utils.PacketSendUtility;

import ai.ActionItemNpcAI;

/**
 * OneVsOne Portal AI Handler
 * 
 * Handles interactions with portal 207011 for the OneVsOne PvP system.
 * Players can click the portal to join the queue for 1v1 matches.
 * 
 * <AUTHOR> System
 */
@AIName("onevsone_portal")
public class OneVsOnePortalAI extends ActionItemNpcAI {

    public OneVsOnePortalAI(Npc owner) {
        super(owner);
    }

    @Override
    protected void handleUseItemFinish(Player player) {
        if (!CustomConfig.ONEVSONE_ENABLED) {
            PacketSendUtility.sendMessage(player, 
                "OneVsOne system is currently disabled.", 
                ChatType.BRIGHT_YELLOW_CENTER);
            return;
        }

        OneVsOneService service = OneVsOneService.getInstance();
        
        if (!service.isSystemActive()) {
            PacketSendUtility.sendMessage(player, 
                "OneVsOne system is not currently active. Please try again later.", 
                ChatType.BRIGHT_YELLOW_CENTER);
            return;
        }

        // Check level requirement
        if (player.getLevel() < CustomConfig.ONEVSONE_MIN_LEVEL) {
            PacketSendUtility.sendMessage(player,
                String.format("You must be level %d or higher to participate in OneVsOne matches.", 
                    CustomConfig.ONEVSONE_MIN_LEVEL),
                ChatType.BRIGHT_YELLOW_CENTER);
            return;
        }

        // Check if player can join queue - removed detailed status check
        if (!service.canJoinQueue(player)) {
            PacketSendUtility.sendMessage(player, 
                "You are already in the OneVsOne queue or in an active match.", 
                ChatType.BRIGHT_YELLOW_CENTER);
            return;
        }

        // Show system info before joining
        showSystemInfo(player, service);

        // Try to join the queue
        boolean success = service.joinQueue(player);
        if (!success) {
            PacketSendUtility.sendMessage(player, 
                "Failed to join OneVsOne queue. Please try again.", 
                ChatType.BRIGHT_YELLOW_CENTER);
        }
    }

    /**
     * Show OneVsOne system information to the player
     */
    private void showSystemInfo(Player player, OneVsOneService service) {
        StringBuilder info = new StringBuilder();
        info.append("=== OneVsOne PvP System ===\n");
        info.append("Players in queue: ").append(service.getQueueSize()).append("\n");
        info.append("Active matches: ").append(service.getActiveMatchCount()).append("\n");
        info.append("Match duration: ").append(CustomConfig.ONEVSONE_MATCH_DURATION_MINUTES).append(" minutes\n");
        info.append("Queue timeout: ").append(CustomConfig.ONEVSONE_QUEUE_TIMEOUT_MINUTES).append(" minutes\n");
        
        if (CustomConfig.ONEVSONE_CROSS_FACTION) {
            info.append("Cross-faction matches: Enabled\n");
        } else {
            info.append("Cross-faction matches: Disabled\n");
        }
        
        info.append("\nLevel requirement: ").append(CustomConfig.ONEVSONE_MIN_LEVEL).append("+\n");
        info.append("Max level difference: ").append(CustomConfig.ONEVSONE_MAX_LEVEL_DIFF).append("\n\n");
        info.append("Joining queue...");

        PacketSendUtility.sendMessage(player, info.toString(), ChatType.BRIGHT_YELLOW_CENTER);
    }

    @Override
    protected void handleDialogStart(Player player) {
        // Show quick info when player approaches the portal
        if (CustomConfig.ONEVSONE_ENABLED) {
            OneVsOneService service = OneVsOneService.getInstance();
            if (service.isSystemActive()) {
                PacketSendUtility.sendMessage(player, 
                    String.format("OneVsOne Portal - Queue: %d players, Active matches: %d", 
                        service.getQueueSize(), service.getActiveMatchCount()), 
                    ChatType.BRIGHT_YELLOW_CENTER);
            } else {
                PacketSendUtility.sendMessage(player, 
                    "OneVsOne Portal - System is currently inactive", 
                    ChatType.BRIGHT_YELLOW_CENTER);
            }
        } else {
            PacketSendUtility.sendMessage(player, 
                "OneVsOne Portal - System is disabled", 
                ChatType.BRIGHT_YELLOW_CENTER);
        }
        
        super.handleDialogStart(player);
    }

    @Override
    protected void handleSpawned() {
        super.handleSpawned();
        // Portal name is set via the NPC template configuration
        // Custom names can be configured in the npc_template.xml if needed
    }
}
