package admincommands;

import java.util.Map;

import com.aionemu.gameserver.configs.main.CustomConfig;
import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.model.onevsone.OneVsOneParticipant;
import com.aionemu.gameserver.services.OneVsOneService;
import com.aionemu.gameserver.utils.chathandlers.AdminCommand;
import com.aionemu.gameserver.world.World;

/**
 * OneVsOne Admin Commands
 * 
 * Provides administrative commands for managing the OneVsOne PvP system.
 * 
 * Commands:
 * //1v1 start - Start the OneVsOne system
 * //1v1 stop - Stop the OneVsOne system
 * //1v1 status - Show system status
 * //1v1 queue - Show queue information
 * //1v1 matches - Show active matches
 * //1v1 stats <player> - Show player statistics
 * //1v1 remove <player> - Remove player from queue/match
 * //1v1 fixdeath <player> - Fix player stuck in death state
 * //1v1 info - Show configuration information
 * 
 * <AUTHOR> System
 */
public class OneVsOne extends AdminCommand {

    public OneVsOne() {
        super("1v1");
    }

    @Override
    public void execute(Player admin, String... params) {
        if (params.length == 0) {
            sendInfo(admin);
            return;
        }

        String command = params[0].toLowerCase();
        OneVsOneService service = OneVsOneService.getInstance();

        switch (command) {
            case "status":
                showStatus(admin, service);
                break;
                
            case "start":
                startSystem(admin, service);
                break;
                
            case "stop":
                stopSystem(admin, service);
                break;
                
            case "queue":
                showQueue(admin, service);
                break;
                
            case "matches":
                showMatches(admin, service);
                break;
                
            case "stats":
                if (params.length < 2) {
                    sendInfo(admin, "Usage: //1v1 stats <player>");
                    return;
                }
                showPlayerStats(admin, service, params[1]);
                break;
                
            case "remove":
                if (params.length < 2) {
                    sendInfo(admin, "Usage: //1v1 remove <player>");
                    return;
                }
                removePlayer(admin, service, params[1]);
                break;
                
            case "fixdeath":
                if (params.length < 2) {
                    sendInfo(admin, "Usage: //1v1 fixdeath <player>");
                    return;
                }
                fixPlayerDeathState(admin, service, params[1]);
                break;

            case "info":
                showConfiguration(admin);
                break;

            default:
                sendInfo(admin);
                break;
        }
    }

    private void sendInfo(Player admin) {
        sendInfo(admin, "OneVsOne Admin Commands:");
        sendInfo(admin, "//1v1 start - Start the OneVsOne system");
        sendInfo(admin, "//1v1 stop - Stop the OneVsOne system");
        sendInfo(admin, "//1v1 status - Show system status");
        sendInfo(admin, "//1v1 queue - Show queue information");
        sendInfo(admin, "//1v1 matches - Show active matches");
        sendInfo(admin, "//1v1 stats <player> - Show player statistics");
        sendInfo(admin, "//1v1 remove <player> - Remove player from queue/match");
        sendInfo(admin, "//1v1 fixdeath <player> - Fix player stuck in death state");
        sendInfo(admin, "//1v1 info - Show configuration information");
    }

    private void showStatus(Player admin, OneVsOneService service) {
        sendInfo(admin, "=== OneVsOne System Status ===");
        sendInfo(admin, "System enabled: " + CustomConfig.ONEVSONE_ENABLED);
        sendInfo(admin, "System active: " + service.isSystemActive());
        sendInfo(admin, "Players in queue: " + service.getQueueSize());
        sendInfo(admin, "Active matches: " + service.getActiveMatchCount());
    }

    private void startSystem(Player admin, OneVsOneService service) {
        if (!CustomConfig.ONEVSONE_ENABLED) {
            sendInfo(admin, "OneVsOne system is disabled in configuration.");
            return;
        }
        
        if (service.isSystemActive()) {
            sendInfo(admin, "OneVsOne system is already active.");
            return;
        }
        
        service.startSystem();
        sendInfo(admin, "OneVsOne system started successfully.");
    }

    private void stopSystem(Player admin, OneVsOneService service) {
        if (!service.isSystemActive()) {
            sendInfo(admin, "OneVsOne system is not active.");
            return;
        }
        
        service.stopSystem();
        sendInfo(admin, "OneVsOne system stopped successfully.");
    }

    private void showQueue(Player admin, OneVsOneService service) {
        sendInfo(admin, "=== OneVsOne Queue ===");
        sendInfo(admin, "Total players in queue: " + service.getQueueSize());
        
        if (service.getQueueSize() == 0) {
            sendInfo(admin, "Queue is empty.");
            return;
        }
        
        sendInfo(admin, "Queue functionality available - use service methods to get detailed info");
    }

    private void showMatches(Player admin, OneVsOneService service) {
        sendInfo(admin, "=== Active OneVsOne Matches ===");
        sendInfo(admin, "Total active matches: " + service.getActiveMatchCount());
        
        if (service.getActiveMatchCount() == 0) {
            sendInfo(admin, "No active matches.");
            return;
        }
        
        sendInfo(admin, "Match details available - use service methods to get detailed info");
    }

    private void showPlayerStats(Player admin, OneVsOneService service, String playerName) {
        Player targetPlayer = World.getInstance().getPlayer(playerName);
        if (targetPlayer == null) {
            sendInfo(admin, "Player '" + playerName + "' not found or not online.");
            return;
        }
        
        sendInfo(admin, "=== OneVsOne Stats for " + playerName + " ===");
        sendInfo(admin, "In queue: " + service.isPlayerInQueue(targetPlayer.getObjectId()));
        sendInfo(admin, "In match: " + service.isPlayerInMatch(targetPlayer.getObjectId()));
    }

    private void removePlayer(Player admin, OneVsOneService service, String playerName) {
        Player targetPlayer = World.getInstance().getPlayer(playerName);
        if (targetPlayer == null) {
            sendInfo(admin, "Player '" + playerName + "' not found or not online.");
            return;
        }
        
        boolean removedFromQueue = service.leaveQueue(targetPlayer);
        if (removedFromQueue) {
            sendInfo(admin, "Removed " + playerName + " from OneVsOne queue.");
        } else {
            sendInfo(admin, playerName + " was not in the OneVsOne queue.");
        }
    }

    private void fixPlayerDeathState(Player admin, OneVsOneService service, String playerName) {
        Player targetPlayer = World.getInstance().getPlayer(playerName);
        if (targetPlayer == null) {
            sendInfo(admin, "Player '" + playerName + "' not found or not online.");
            return;
        }
        
        if (targetPlayer.getLifeStats().isDead()) {
            // Force resurrect the player
            com.aionemu.gameserver.services.player.PlayerReviveService.revive(targetPlayer, 100, 100, false, 0);
            sendInfo(admin, "Fixed death state for " + playerName + ".");
        } else {
            sendInfo(admin, playerName + " is not dead.");
        }
    }

    private void showConfiguration(Player admin) {
        sendInfo(admin, "=== OneVsOne Configuration ===");
        sendInfo(admin, "Enabled: " + CustomConfig.ONEVSONE_ENABLED);
        sendInfo(admin, "Min Level: " + CustomConfig.ONEVSONE_MIN_LEVEL);
        sendInfo(admin, "Max Level Diff: " + CustomConfig.ONEVSONE_MAX_LEVEL_DIFF);
        sendInfo(admin, "Queue Timeout: " + CustomConfig.ONEVSONE_QUEUE_TIMEOUT_MINUTES + " minutes");
        sendInfo(admin, "Match Duration: " + CustomConfig.ONEVSONE_MATCH_DURATION_MINUTES + " minutes");
        sendInfo(admin, "Cross Faction: " + CustomConfig.ONEVSONE_CROSS_FACTION);
        sendInfo(admin, "Auto Start: " + CustomConfig.ONEVSONE_AUTO_START);
        sendInfo(admin, "Announcements: " + CustomConfig.ONEVSONE_ANNOUNCEMENTS);
    }
}
