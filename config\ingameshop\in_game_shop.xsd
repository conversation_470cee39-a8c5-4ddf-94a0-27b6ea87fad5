<?xml version="1.0" standalone="yes"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:jxb="http://java.sun.com/xml/ns/jaxb" jxb:version="2.1">

	<xs:element name="in_game_shop">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="category" type="IGCategory" minOccurs="0" maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:complexType name="IGCategory">
		<xs:sequence>
			<xs:element name="sub_category" type="IGSubCategory" minOccurs="0" maxOccurs="18"/>
		</xs:sequence>
		<xs:attribute name="id" type="xs:int" use="required"/>
		<xs:attribute name="name" type="xs:string" use="required"/>
	</xs:complexType>

	<xs:complexType name="IGSubCategory">
		<xs:attribute name="id" type="xs:int" use="required"/>
		<xs:attribute name="name" type="xs:string" use="required"/>
	</xs:complexType>
</xs:schema>