#
# ----------------------------
# AutoGroup config's:

# Enable Auto Group
# Default: true
gameserver.autogroup.enable = true

# Enable check start time for pvp arenas
# Default: true
gameserver.startTime.enable = true

# Dredgion registration period
# Default: 60 min
gameserver.dredgion.registration_period = 60

# Dredgion start times
# Comma separated list of cron expressions (expressions containing commas must be put in quotes)
# Default: "0 0 0,12,20 ? * *"
gameserver.dredgion.time = "0 0 0,12,20 ? * *"

# Kamar Battlefield registration period
# Default: 60 min
gameserver.kamar_battlefield.registration_period = 60

# Kamar Battlefield start times
# Comma separated list of cron expressions (expressions containing commas must be put in quotes)
# Default: "0 0 0,20 ? * MON,WED,SAT"
gameserver.kamar_battlefield.time = "0 0 0,20 ? * MON,WED,SAT"

# Engulfed Ophidan Bridge registration period
# Default: 60 min
gameserver.engulfed_ophidan_bridge.registration_period = 60

# Engulfed Ophidan Bridge registration start times
# Comma separated list of cron expressions (expressions containing commas must be put in quotes)
# Default: 0 0 12,19 ? * *
gameserver.engulfed_ophidan_bridge.time = "0 0 12,19 ? * *"

# Iron Wall Warfront registration period
# Default: 60 min
gameserver.iron_wall_warfront.registration_period = 60

# Iron Wall Warfront registration start times
# Comma separated list of cron expressions (expressions containing commas must be put in quotes)
# Default: "0 0 0,12 ? * SUN"
gameserver.iron_wall_warfront.time = "0 0 0,12 ? * SUN"

# Idgel Dome registration period
# Default: 60 min
gameserver.idgel_dome.registration_period = 60

# Idgel Dome registration start times
# Comma separated list of cron expressions (expressions containing commas must be put in quotes)
# Default: 0 0 23 ? * *
gameserver.idgel_dome.time = 0 0 23 ? * *

# Announce battlefield registrations to the enemy faction
# Default: false
gameserver.autogroup.announce_battleground_registrations = false