#
# ----------------------------
# Player transfer config's:
# ----------------------------

# if player exceeds this amount in inv+wh - we cant transfer him. 0 to disable
ptransfer.max.kinah = 0
# bind position for elyos
ptransfer.bindpoint.elyos = 210010000 1212.9423 1044.8516 140.75568 32
# bind position for asmos
ptransfer.bindpoint.asmo = 220010000 571.0388 2787.3420 299.8750 32
# allows to transfer emotions
ptransfer.allow.emotions = true
# allows to transfer motions
ptransfer.allow.motions = true
# allows to transfer macrosses
ptransfer.allow.macro = true
# allows to transfer npc factions
ptransfer.allow.npcfactions = true
# allows to transfer pets
ptransfer.allow.pets = true
# allows to transfer recipes
ptransfer.allow.recipes = true
# allows to transfer skills
ptransfer.allow.skills = true
# allows to transfer titles
ptransfer.allow.titles = true
# allows to transfer quests
ptransfer.allow.quests = true
# allows to transfer inventory (cube)
ptransfer.allow.inventory = true
# allows to transfer regular warehouse
ptransfer.allow.warehouse = true
# allows to transfer stigma stones
ptransfer.allow.stigma = true
# if target server had player with same nickname - block or use prefix to change players name
ptransfer.block.samename = false
# time before which this player can be transferred again. 0 to disable
ptransfer.retransfer.hours = 0
# skill list should removed being transferred
# to disable it set ptransfer.remove.skills.list = *
# this will mean this list will be skipped
# enter values separated by ',' ptransfer.remove.skills.list = 1,2,3,4
ptransfer.remove.skills.list = *