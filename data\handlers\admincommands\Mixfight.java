package admincommands;

import com.aionemu.gameserver.configs.main.CustomConfig;
import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.services.MixfightService;
import com.aionemu.gameserver.utils.PacketSendUtility;
import com.aionemu.gameserver.utils.chathandlers.AdminCommand;

/**
 * MixFight Admin Command - Administrative controls for the MixFight event system
 *
 * Usage:
 * //mixfight - Show help and current status
 * //mixfight start - Force start event
 * //mixfight stop - Force stop event
 * //mixfight status - Show detailed event status
 * //mixfight info - Show event information
 * //mixfight leaderboard - Show current leaderboard
 *
 * <AUTHOR> Implementation
 */
public class Mixfight extends AdminCommand {

    public Mixfight() {
        super("mixfight");
    }

    @Override
    public void execute(Player admin, String... params) {
        MixfightService mixfightService = MixfightService.getInstance();

        if (mixfightService == null) {
            PacketSendUtility.sendMessage(admin, "MixFight service is not available.");
            return;
        }

        if (params.length == 0) {
            showHelp(admin, mixfightService);
            return;
        }

        String command = params[0].toLowerCase();

        switch (command) {
            case "start":
                handleStartCommand(admin, mixfightService);
                break;

            case "stop":
                handleStopCommand(admin, mixfightService);
                break;

            case "status":
                handleStatusCommand(admin, mixfightService);
                break;

            case "info":
                handleInfoCommand(admin, mixfightService);
                break;

            case "leaderboard":
            case "lb":
                handleLeaderboardCommand(admin, mixfightService);
                break;

            case "help":
            default:
                showHelp(admin, mixfightService);
                break;
        }
    }

    /**
     * Show command help and current status
     */
    private void showHelp(Player admin, MixfightService mixfightService) {
        String currentStatus = mixfightService.getEventStatus();

        PacketSendUtility.sendMessage(admin,
            "=== MixFight Admin Commands ===\n" +
            "Current Status: " + currentStatus + "\n" +
            "\n" +
            "Available Commands:\n" +
            "//mixfight start - Force start event\n" +
            "//mixfight stop - Force stop event\n" +
            "//mixfight status - Show detailed status\n" +
            "//mixfight info - Show event information\n" +
            "//mixfight leaderboard - Show leaderboard\n" +
            "//mixfight help - Show this help"
        );
    }

    /**
     * Handle start command
     */
    private void handleStartCommand(Player admin, MixfightService mixfightService) {
        if (mixfightService.isEventActive()) {
            PacketSendUtility.sendMessage(admin, "MixFight event is already active.");
            return;
        }

        if (mixfightService.arePortalsActive()) {
            PacketSendUtility.sendMessage(admin, "MixFight portals are already active. Event will start automatically.");
            return;
        }

        if (mixfightService.forceStartEvent()) {
            PacketSendUtility.sendMessage(admin, "MixFight event sequence started successfully!");
            
            // Log admin action
            PacketSendUtility.sendMessage(admin, 
                "Event will begin with portal phase, followed by the actual event. " +
                "Players can join through the portals in Sanctum and Pandaemonium."
            );
        } else {
            PacketSendUtility.sendMessage(admin, "Failed to start MixFight event. Check server logs for details.");
        }
    }

    /**
     * Handle stop command
     */
    private void handleStopCommand(Player admin, MixfightService mixfightService) {
        if (!mixfightService.isEventActive() && !mixfightService.arePortalsActive()) {
            PacketSendUtility.sendMessage(admin, "No MixFight event is currently active.");
            return;
        }

        int participantCount = mixfightService.getParticipantCount();

        if (mixfightService.forceStopEvent()) {
            PacketSendUtility.sendMessage(admin, "MixFight event stopped successfully!");

            // Log admin action
            if (participantCount > 0) {
                PacketSendUtility.sendMessage(admin,
                    String.format("All %d participants have been teleported out and rewards will be distributed based on current standings.",
                        participantCount)
                );
            } else {
                PacketSendUtility.sendMessage(admin, "No participants were in the event.");
            }
        } else {
            PacketSendUtility.sendMessage(admin, "Failed to stop MixFight event. Check server logs for details.");
        }
    }

    /**
     * Handle status command
     */
    private void handleStatusCommand(Player admin, MixfightService mixfightService) {
        String status = mixfightService.getEventStatus();
        int participantCount = mixfightService.getParticipantCount();
        boolean eventActive = mixfightService.isEventActive();
        boolean portalsActive = mixfightService.arePortalsActive();

        StringBuilder sb = new StringBuilder();
        sb.append("=== MixFight Event Status ===\n");
        sb.append("Status: ").append(status).append("\n");
        sb.append("Event Active: ").append(eventActive ? "Yes" : "No").append("\n");
        sb.append("Portals Active: ").append(portalsActive ? "Yes" : "No").append("\n");
        sb.append("Participants: ").append(participantCount).append("\n");

        // Add configuration info
        sb.append("\n=== Configuration ===\n");
        sb.append("Enabled: ").append(CustomConfig.MIXFIGHT_ENABLED ? "Yes" : "No").append("\n");
        sb.append("Min Level: ").append(CustomConfig.MIXFIGHT_MIN_LEVEL).append("\n");
        sb.append("Max Players: ").append(CustomConfig.MIXFIGHT_MAX_PLAYERS).append("\n");
        sb.append("Duration: ").append(CustomConfig.MIXFIGHT_DURATION_MINUTES).append(" minutes\n");
        sb.append("Portal Duration: ").append(CustomConfig.MIXFIGHT_PORTAL_DURATION_MINUTES).append(" minutes\n");
        sb.append("Schedule: ").append(CustomConfig.MIXFIGHT_SCHEDULE).append("\n");

        if (eventActive || portalsActive) {
            sb.append("\n=== Active Participants ===\n");
            var leaderboard = mixfightService.getLeaderboard();

            if (leaderboard.isEmpty()) {
                sb.append("No participants yet.\n");
            } else {
                int rank = 1;
                for (var participant : leaderboard) {
                    sb.append(String.format("%d. %s - %d AP (%d kills)\n",
                        rank, participant.getPlayerName(),
                        participant.getAccumulatedAP(), participant.getKills()));
                    rank++;

                    if (rank > 15) { // Limit to top 15 for admin view
                        sb.append("... and ").append(leaderboard.size() - 15).append(" more\n");
                        break;
                    }
                }
            }
        }

        PacketSendUtility.sendMessage(admin, sb.toString());
    }

    /**
     * Handle info command
     */
    private void handleInfoCommand(Player admin, MixfightService mixfightService) {
        PacketSendUtility.sendMessage(admin,
            "=== MixFight Event Information ===\n" +
            "MixFight is a scheduled PvP event system where players from all factions\n" +
            "can fight each other in dedicated arena maps.\n" +
            "\n" +
            "Event Flow:\n" +
            "1. Portals open in Sanctum and Pandaemonium\n" +
            "2. Players can register during portal phase\n" +
            "3. Event starts automatically after portal duration\n" +
            "4. All participants are teleported to random arena map\n" +
            "5. Free-for-all combat with points system\n" +
            "6. Rewards distributed based on final ranking\n" +
            "\n" +
            "Features:\n" +
            "- Cross-faction combat\n" +
            "- Multiple arena maps with random selection\n" +
            "- Points-based leaderboard system\n" +
            "- Rank-based reward distribution\n" +
            "- Configurable schedule and settings\n" +
            "\n" +
            "Current Status: " + mixfightService.getEventStatus()
        );
    }

    /**
     * Handle leaderboard command
     */
    private void handleLeaderboardCommand(Player admin, MixfightService mixfightService) {
        if (!mixfightService.isEventActive() && !mixfightService.arePortalsActive()) {
            PacketSendUtility.sendMessage(admin, "No active MixFight event. Leaderboard is empty.");
            return;
        }

        var leaderboard = mixfightService.getLeaderboard();
        
        if (leaderboard.isEmpty()) {
            PacketSendUtility.sendMessage(admin, "No participants in current event.");
            return;
        }

        StringBuilder sb = new StringBuilder();
        sb.append("=== MixFight Leaderboard (Admin View) ===\n");
        sb.append("Total Participants: ").append(leaderboard.size()).append("\n\n");
        
        int rank = 1;
        for (var participant : leaderboard) {
            sb.append(String.format("%d. %s - %d AP (%d kills)\n", 
                rank, participant.getPlayerName(), 
                participant.getAccumulatedAP(), participant.getKills()));
            rank++;
            
            if (rank > 20) { // Show top 20 for admin
                sb.append("... and ").append(leaderboard.size() - 20).append(" more participants\n");
                break;
            }
        }

        PacketSendUtility.sendMessage(admin, sb.toString());
    }
}
