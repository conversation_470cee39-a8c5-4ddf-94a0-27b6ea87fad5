#
# ----------------------------
# Housing config's:
# ----------------------------

# House render visibility range. The bigger the value, the more packets will be sent during login.
# Default: 200
gameserver.housing.visibility.distance = 200

# Auction for houses
# Default: true
gameserver.housing.auction.enable = true

# Pay rent
# Default: true
gameserver.housing.pay.enable = true

# Auction end day/time
# Default: 0 0 12 ? * SUN (each Sunday at 12 midday)
gameserver.housing.auction.end_time = 0 0 12 ? * SUN

# Days when registering houses in auction is allowed for players
# Default: 1, 5 (Monday till Friday)
gameserver.housing.auction.register_days = 1, 5

# House maintenance frequency
# Default: 0 0 0 ? * MON (each Sunday at midnight) 
gameserver.housing.maintain.time = 0 0 0 ? * MON

# Auction bid step limit (bid can not exceed 100% of the current price)
# Default: 100%
gameserver.housing.auction.steplimit = 100

# Auction default bid prices
# Note: If set to zero 12000000 is used from parsed data
gameserver.housing.auction.default_bid.house = 0
# Note: If set to zero 112000000 is used from parsed data
gameserver.housing.auction.default_bid.mansion = 0
# Note: If set to zero 335000000 is used from parsed data
gameserver.housing.auction.default_bid.estate = 0
# Note: If set to zero 1000000000 is used from parsed data
gameserver.housing.auction.default_bid.palace = 0

# Auction minimal level required for bidding
# Note: If set to zero 21 is used from parsed data
gameserver.housing.auction.bidding.min_level.house = 0
# Note: If set to zero 30 is used from parsed data
gameserver.housing.auction.bidding.min_level.mansion = 0
# Note: If set to zero 40 is used from parsed data
gameserver.housing.auction.bidding.min_level.estate = 0
# Note: If set to zero 50 is used from parsed data
gameserver.housing.auction.bidding.min_level.palace = 0

# Registration fee based on the auction starting price
# Default: 0.3 (30%)
gameserver.housing.auction.registration_fee = 0.3

# Sales commission of the winning bid (subtracted on successful sale from the reward amount)
# Default: 0.1 (10%)
gameserver.housing.auction.sales_commission = 0.1

# Refund percentage of the auction starting price if a player loses his house without successfully selling it (due to grace time)
# Default: 0.5 (50%)
gameserver.housing.auction.grace_end_refund = 0.5

# Time when housing broker will be filled with new unoccupied houses. Leave empty to disable
# Default: 0 0 0 ? * MON
gameserver.housing.auction.auto_fill.time = 0 0 0 ? * MON
# Maximum number of houses to add per house type
gameserver.housing.auction.auto_fill.limit.HOUSE = 20
gameserver.housing.auction.auto_fill.limit.MANSION = 10
gameserver.housing.auction.auto_fill.limit.ESTATE = 5
gameserver.housing.auction.auto_fill.limit.PALACE = 1